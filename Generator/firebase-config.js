// Firebase Configuration and Initialization for eHroby PWA
// Handles Firestore, Storage, and Messaging

// Import Firebase modules (using CDN for compatibility)
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import { getFirestore, collection, doc, addDoc, updateDoc, deleteDoc, getDocs, getDoc, onSnapshot, enableNetwork, disableNetwork } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
import { getStorage, ref, uploadBytes, getDownloadURL, deleteObject } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js';
import { getMessaging, getToken, onMessage } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging.js';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyA6jYAZP3Ldm2YAVNfTYU5kTYGrNwmVTmk",
  authDomain: "ehroby-e3388.firebaseapp.com",
  projectId: "ehroby-e3388",
  storageBucket: "ehroby-e3388.firebasestorage.app",
  messagingSenderId: "275727532271",
  appId: "1:275727532271:web:a6a88c530d4b459fb61b1a",
  measurementId: "G-5ZEBBJYVS1"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const storage = getStorage(app);
let messaging = null;

// Initialize messaging if supported
try {
  messaging = getMessaging(app);
} catch (error) {
  console.warn('Firebase Messaging not supported:', error);
}

// Firebase service class
class FirebaseService {
  constructor() {
    this.isOnline = navigator.onLine;
    this.setupNetworkListeners();
    this.setupOfflineSupport();
  }

  // Setup network status listeners
  setupNetworkListeners() {
    window.addEventListener('online', () => {
      this.isOnline = true;
      console.log('Firebase: Back online, enabling network');
      enableNetwork(db);
      this.syncOfflineData();
    });

    window.addEventListener('offline', () => {
      this.isOnline = false;
      console.log('Firebase: Gone offline, disabling network');
      disableNetwork(db);
    });
  }

  // Setup offline support
  setupOfflineSupport() {
    // Enable offline persistence
    try {
      enableNetwork(db);
    } catch (error) {
      console.warn('Firebase offline persistence error:', error);
    }
  }

  // CUSTOMERS CRUD OPERATIONS
  async getCustomers() {
    try {
      const customersRef = collection(db, 'customers');
      const snapshot = await getDocs(customersRef);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting customers:', error);
      return this.getOfflineCustomers();
    }
  }

  async addCustomer(customerData) {
    try {
      const customersRef = collection(db, 'customers');
      const docRef = await addDoc(customersRef, {
        ...customerData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error adding customer:', error);
      return this.addOfflineCustomer(customerData);
    }
  }

  async updateCustomer(customerId, updates) {
    try {
      const customerRef = doc(db, 'customers', customerId);
      await updateDoc(customerRef, {
        ...updates,
        updatedAt: new Date()
      });
      return true;
    } catch (error) {
      console.error('Error updating customer:', error);
      return this.updateOfflineCustomer(customerId, updates);
    }
  }

  async deleteCustomer(customerId) {
    try {
      const customerRef = doc(db, 'customers', customerId);
      await deleteDoc(customerRef);
      return true;
    } catch (error) {
      console.error('Error deleting customer:', error);
      return this.deleteOfflineCustomer(customerId);
    }
  }

  // TASKS CRUD OPERATIONS
  async getTasks() {
    try {
      const tasksRef = collection(db, 'tasks');
      const snapshot = await getDocs(tasksRef);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting tasks:', error);
      return this.getOfflineTasks();
    }
  }

  async addTask(taskData) {
    try {
      const tasksRef = collection(db, 'tasks');
      const docRef = await addDoc(tasksRef, {
        ...taskData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error adding task:', error);
      return this.addOfflineTask(taskData);
    }
  }

  async updateTask(taskId, updates) {
    try {
      const taskRef = doc(db, 'tasks', taskId);
      await updateDoc(taskRef, {
        ...updates,
        updatedAt: new Date()
      });
      return true;
    } catch (error) {
      console.error('Error updating task:', error);
      return this.updateOfflineTask(taskId, updates);
    }
  }

  // PAYMENTS CRUD OPERATIONS
  async getPayments() {
    try {
      const paymentsRef = collection(db, 'payments');
      const snapshot = await getDocs(paymentsRef);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error getting payments:', error);
      return this.getOfflinePayments();
    }
  }

  async addPayment(paymentData) {
    try {
      const paymentsRef = collection(db, 'payments');
      const docRef = await addDoc(paymentsRef, {
        ...paymentData,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      return docRef.id;
    } catch (error) {
      console.error('Error adding payment:', error);
      return this.addOfflinePayment(paymentData);
    }
  }

  async updatePayment(paymentId, updates) {
    try {
      const paymentRef = doc(db, 'payments', paymentId);
      await updateDoc(paymentRef, {
        ...updates,
        updatedAt: new Date()
      });
      return true;
    } catch (error) {
      console.error('Error updating payment:', error);
      return this.updateOfflinePayment(paymentId, updates);
    }
  }

  // PHOTO STORAGE OPERATIONS
  async uploadPhoto(file, taskId) {
    try {
      const photoRef = ref(storage, `photos/${taskId}/${Date.now()}_${file.name}`);
      const snapshot = await uploadBytes(photoRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      // Save photo metadata to Firestore
      const photoData = {
        taskId,
        url: downloadURL,
        filename: file.name,
        size: file.size,
        uploadedAt: new Date()
      };
      
      const photosRef = collection(db, 'photos');
      const docRef = await addDoc(photosRef, photoData);
      
      return { id: docRef.id, ...photoData };
    } catch (error) {
      console.error('Error uploading photo:', error);
      return this.storeOfflinePhoto(file, taskId);
    }
  }

  async deletePhoto(photoId, photoUrl) {
    try {
      // Delete from Storage
      const photoRef = ref(storage, photoUrl);
      await deleteObject(photoRef);
      
      // Delete from Firestore
      const photoDocRef = doc(db, 'photos', photoId);
      await deleteDoc(photoDocRef);
      
      return true;
    } catch (error) {
      console.error('Error deleting photo:', error);
      return false;
    }
  }

  // REAL-TIME LISTENERS
  onCustomersChange(callback) {
    const customersRef = collection(db, 'customers');
    return onSnapshot(customersRef, (snapshot) => {
      const customers = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      callback(customers);
    }, (error) => {
      console.error('Error listening to customers:', error);
    });
  }

  onTasksChange(callback) {
    const tasksRef = collection(db, 'tasks');
    return onSnapshot(tasksRef, (snapshot) => {
      const tasks = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
      callback(tasks);
    }, (error) => {
      console.error('Error listening to tasks:', error);
    });
  }

  // OFFLINE FALLBACK METHODS (will be implemented with IndexedDB)
  getOfflineCustomers() {
    return JSON.parse(localStorage.getItem('offline_customers') || '[]');
  }

  addOfflineCustomer(customerData) {
    const customers = this.getOfflineCustomers();
    const newCustomer = { id: 'offline_' + Date.now(), ...customerData };
    customers.push(newCustomer);
    localStorage.setItem('offline_customers', JSON.stringify(customers));
    this.queueForSync('customers', 'add', newCustomer);
    return newCustomer.id;
  }

  updateOfflineCustomer(customerId, updates) {
    const customers = this.getOfflineCustomers();
    const index = customers.findIndex(c => c.id === customerId);
    if (index !== -1) {
      customers[index] = { ...customers[index], ...updates };
      localStorage.setItem('offline_customers', JSON.stringify(customers));
      this.queueForSync('customers', 'update', { id: customerId, ...updates });
    }
    return true;
  }

  deleteOfflineCustomer(customerId) {
    const customers = this.getOfflineCustomers();
    const filtered = customers.filter(c => c.id !== customerId);
    localStorage.setItem('offline_customers', JSON.stringify(filtered));
    this.queueForSync('customers', 'delete', { id: customerId });
    return true;
  }

  getOfflineTasks() {
    return JSON.parse(localStorage.getItem('offline_tasks') || '[]');
  }

  addOfflineTask(taskData) {
    const tasks = this.getOfflineTasks();
    const newTask = { id: 'offline_' + Date.now(), ...taskData };
    tasks.push(newTask);
    localStorage.setItem('offline_tasks', JSON.stringify(tasks));
    this.queueForSync('tasks', 'add', newTask);
    return newTask.id;
  }

  updateOfflineTask(taskId, updates) {
    const tasks = this.getOfflineTasks();
    const index = tasks.findIndex(t => t.id === taskId);
    if (index !== -1) {
      tasks[index] = { ...tasks[index], ...updates };
      localStorage.setItem('offline_tasks', JSON.stringify(tasks));
      this.queueForSync('tasks', 'update', { id: taskId, ...updates });
    }
    return true;
  }

  getOfflinePayments() {
    return JSON.parse(localStorage.getItem('offline_payments') || '[]');
  }

  addOfflinePayment(paymentData) {
    const payments = this.getOfflinePayments();
    const newPayment = { id: 'offline_' + Date.now(), ...paymentData };
    payments.push(newPayment);
    localStorage.setItem('offline_payments', JSON.stringify(payments));
    this.queueForSync('payments', 'add', newPayment);
    return newPayment.id;
  }

  updateOfflinePayment(paymentId, updates) {
    const payments = this.getOfflinePayments();
    const index = payments.findIndex(p => p.id === paymentId);
    if (index !== -1) {
      payments[index] = { ...payments[index], ...updates };
      localStorage.setItem('offline_payments', JSON.stringify(payments));
      this.queueForSync('payments', 'update', { id: paymentId, ...updates });
    }
    return true;
  }

  storeOfflinePhoto(file, taskId) {
    // Store photo in IndexedDB for later upload
    const photoData = {
      id: 'offline_' + Date.now(),
      taskId,
      file,
      filename: file.name,
      size: file.size,
      storedAt: new Date()
    };
    
    this.queueForSync('photos', 'add', photoData);
    return photoData;
  }

  // SYNC QUEUE MANAGEMENT
  queueForSync(collection, operation, data) {
    const syncQueue = JSON.parse(localStorage.getItem('sync_queue') || '[]');
    syncQueue.push({
      id: Date.now(),
      collection,
      operation,
      data,
      timestamp: new Date()
    });
    localStorage.setItem('sync_queue', JSON.stringify(syncQueue));
  }

  async syncOfflineData() {
    if (!this.isOnline) return;

    const syncQueue = JSON.parse(localStorage.getItem('sync_queue') || '[]');
    
    for (const item of syncQueue) {
      try {
        await this.processSyncItem(item);
        // Remove from queue after successful sync
        const updatedQueue = syncQueue.filter(q => q.id !== item.id);
        localStorage.setItem('sync_queue', JSON.stringify(updatedQueue));
      } catch (error) {
        console.error('Error syncing item:', error);
      }
    }
  }

  async processSyncItem(item) {
    const { collection, operation, data } = item;
    
    switch (operation) {
      case 'add':
        if (collection === 'customers') await this.addCustomer(data);
        else if (collection === 'tasks') await this.addTask(data);
        else if (collection === 'payments') await this.addPayment(data);
        break;
      case 'update':
        if (collection === 'customers') await this.updateCustomer(data.id, data);
        else if (collection === 'tasks') await this.updateTask(data.id, data);
        else if (collection === 'payments') await this.updatePayment(data.id, data);
        break;
      case 'delete':
        if (collection === 'customers') await this.deleteCustomer(data.id);
        break;
    }
  }

  // PUSH NOTIFICATIONS
  async requestNotificationPermission() {
    if (!messaging) return null;

    try {
      const permission = await Notification.requestPermission();
      if (permission === 'granted') {
        const token = await getToken(messaging, {
          vapidKey: 'YOUR_VAPID_KEY' // You'll need to generate this in Firebase Console
        });
        console.log('FCM Token:', token);
        return token;
      }
    } catch (error) {
      console.error('Error getting notification permission:', error);
    }
    return null;
  }

  setupMessageListener() {
    if (!messaging) return;

    onMessage(messaging, (payload) => {
      console.log('Message received:', payload);
      
      // Show notification
      if (Notification.permission === 'granted') {
        new Notification(payload.notification.title, {
          body: payload.notification.body,
          icon: 'https://storage.googleapis.com/espomienka/logo36.png'
        });
      }
    });
  }
}

// Create global Firebase service instance
const firebaseService = new FirebaseService();

// Export for use in other modules
window.firebaseService = firebaseService;

export default firebaseService;
