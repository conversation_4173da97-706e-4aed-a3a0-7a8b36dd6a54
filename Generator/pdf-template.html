<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cenov<PERSON> ponuka</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .pdf-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }
        
        .header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #4a1e4a 0%, #5e2e60 100%);
            border-radius: 8px;
            color: white;
        }
        
        .logo {
            width: auto;
            height: 60px;
            max-width: 80px;
            margin-right: 20px;
            border-radius: 8px;
            object-fit: contain; /* Prevents logo squishing */
            filter: brightness(0) invert(1); /* White logo on purple background */
        }
        
        .header-text {
            flex: 1;
        }
        
        .header-text h1 {
            font-size: 24px;
            font-weight: 700;
            color: white;
            margin-bottom: 5px;
        }

        .header-text .subtitle {
            font-size: 14px;
            color: white;
            font-weight: 400;
            opacity: 0.9;
        }
        
        .customer-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #5e2e60;
        }
        
        .customer-info h2 {
            font-size: 16px;
            font-weight: 600;
            color: #5e2e60;
            margin-bottom: 15px;
        }
        
        .customer-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .customer-field {
            display: flex;
            align-items: center;
        }
        
        .customer-field strong {
            min-width: 80px;
            color: #333;
            font-weight: 500;
        }
        
        .services-section {
            margin-bottom: 30px;
        }
        
        .services-section h2 {
            font-size: 18px;
            font-weight: 600;
            color: #5e2e60;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .services-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .services-table th {
            background: #5e2e60;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            font-size: 11px;
        }
        
        .services-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #e0e0e0;
            font-size: 11px;
        }
        
        .services-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .service-name {
            font-weight: 500;
        }
        
        .service-price {
            text-align: right;
            font-weight: 600;
            color: #5e2e60;
        }
        
        .totals-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px solid #5e2e60;
        }
        
        .totals-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .totals-table td {
            padding: 8px 0;
            font-size: 12px;
        }
        
        .totals-table .label {
            font-weight: 500;
            color: #333;
        }
        
        .totals-table .amount {
            text-align: right;
            font-weight: 600;
        }
        
        .total-final {
            border-top: 2px solid #5e2e60;
            padding-top: 12px !important;
            margin-top: 8px;
        }
        
        .total-final .label {
            font-size: 14px;
            font-weight: 700;
            color: #5e2e60;
        }
        
        .total-final .amount {
            font-size: 16px;
            font-weight: 700;
            color: #5e2e60;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            font-size: 11px;
            color: #666;
        }
        
        .footer-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .footer-section h3 {
            font-size: 12px;
            font-weight: 600;
            color: #5e2e60;
            margin-bottom: 8px;
        }
        
        .contact-info {
            line-height: 1.6;
        }
        
        .validity-info {
            text-align: right;
        }
        
        /* Print optimizations */
        @media print {
            .pdf-container {
                padding: 15mm;
            }
            
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
        
        /* Ensure proper page breaks */
        .services-table {
            page-break-inside: avoid;
        }
        
        .totals-section {
            page-break-inside: avoid;
        }
    </style>
</head>
<body>
    <div class="pdf-container" id="pdf-content">
        <!-- Header -->
        <div class="header">
            <img src="https://storage.googleapis.com/espomienka/logo36.png" alt="eSpomienka Logo" class="logo">
            <div class="header-text">
                <h1>CENOVÁ PONUKA</h1>
                <div class="subtitle">Starostlivosť o hrobové miesta</div>
            </div>
        </div>
        
        <!-- Customer Information -->
        <div class="customer-info">
            <h2>ÚDAJE O ZÁKAZNÍKOVI</h2>
            <div class="customer-details">
                <div class="customer-field">
                    <strong>Meno:</strong>
                    <span id="customer-name">-</span>
                </div>
                <div class="customer-field">
                    <strong>Telefón:</strong>
                    <span id="customer-phone">-</span>
                </div>
                <div class="customer-field">
                    <strong>Email:</strong>
                    <span id="customer-email">-</span>
                </div>
                <div class="customer-field">
                    <strong>Adresa:</strong>
                    <span id="customer-address">-</span>
                </div>
            </div>
        </div>
        
        <!-- Services -->
        <div class="services-section">
            <h2>VYBRANÉ SLUŽBY</h2>
            <table class="services-table">
                <thead>
                    <tr>
                        <th style="width: 60%">Názov služby</th>
                        <th style="width: 20%">Množstvo</th>
                        <th style="width: 20%">Cena</th>
                    </tr>
                </thead>
                <tbody id="services-list">
                    <!-- Services will be inserted here -->
                </tbody>
            </table>
        </div>
        
        <!-- Totals -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="label">Súčet bez DPH:</td>
                    <td class="amount" id="subtotal">0,00 €</td>
                </tr>
                <tr id="discountRowPdf" style="display: none;">
                    <td class="label">Zľava (<span id="discountPercentPdf">0</span>%):</td>
                    <td class="amount" id="discountAmountPdf" style="color: #dc3545;">-0,00 €</td>
                </tr>
                <tr>
                    <td class="label">Súčet po zľave:</td>
                    <td class="amount" id="subtotalAfterDiscountPdf">0,00 €</td>
                </tr>
                <tr>
                    <td class="label">DPH 20%:</td>
                    <td class="amount" id="vat">0,00 €</td>
                </tr>
                <tr class="total-final">
                    <td class="label">CELKOM S DPH:</td>
                    <td class="amount" id="total">0,00 €</td>
                </tr>
            </table>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <div class="footer-grid">
                <div class="footer-section">
                    <h3>Kontaktné údaje</h3>
                    <div class="contact-info">
                        Email: <EMAIL><br>
                        Telefón: +421 951 553 464
                    </div>
                </div>
                <div class="footer-section">
                    <div class="validity-info">
                        <strong>Platnosť ponuky:</strong> 30 dní<br>
                        <strong>Dátum vystavenia:</strong> <span id="issue-date">-</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
