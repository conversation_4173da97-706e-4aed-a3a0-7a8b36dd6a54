# 🔧 Riešenie problému s PDF generovaním v Electron

## ❌ Problém
PDF generovanie nefungovalo v Electron aplikácii kvôli problémom s načítavaním jsPDF knižnice.

## ✅ Riešenie implementované

### 1. Lokálna inštalácia jsPDF
```bash
npm install jspdf
```

### 2. Fallback mechanizmus
- Primárne: Načítava jsPDF z `node_modules/jspdf/dist/jspdf.umd.min.js`
- Sekundárne: <PERSON>k <PERSON>, načíta z CDN
- Tretie: Error handling s informatívnymi hláseniami

### 3. Vylepšené error handling
- Kontrola dostupnosti jsPDF knižnice
- Detailné chybové hlásenia
- Automatická diagnostika problémov

### 4. Test funkcionalita
- Pridané test menu "Test PDF generovanie"
- Samostatný test súbor `test-pdf.html`
- Možnosť otestovať PDF bez vyplňovania formulára

## 🧪 Testovanie PDF funkcionality

### Metóda 1: Test menu
1. Spustite aplikáciu: `npm start`
2. V menu vyberte: **Pomoc → Test PDF generovanie**
3. Kliknite na "Generovať test PDF"
4. Ak funguje, PDF sa stiahne

### Metóda 2: Hlavná aplikácia
1. Vyplňte údaje o zákazníkovi (meno, telefón, email)
2. Vyberte aspoň jednu službu
3. Kliknite "Generovať PDF ponuku"
4. PDF sa má stiahnuť automaticky

### Metóda 3: Console diagnostika
1. Otvorte Developer Tools (F12)
2. V console hľadajte:
   - ✅ "jsPDF is available and ready"
   - ❌ "jsPDF is not available"

## 🔍 Diagnostika problémov

### Ak sa PDF negeneruje:

#### 1. Skontrolujte console
```javascript
// Otvorte Developer Tools (F12) a spustite:
console.log('jsPDF available:', typeof window.jspdf !== 'undefined');
console.log('jsPDF object:', window.jspdf);
```

#### 2. Skontrolujte sieťové požiadavky
- V Developer Tools → Network tab
- Obnovte stránku
- Hľadajte požiadavky na jsPDF súbory
- Skontrolujte, či sa načítavajú úspešne

#### 3. Manuálny test
```javascript
// V console spustite:
try {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();
    doc.text('Test', 20, 20);
    doc.save('test.pdf');
    console.log('PDF test successful');
} catch (error) {
    console.error('PDF test failed:', error);
}
```

## 🛠️ Možné riešenia problémov

### Problém 1: jsPDF sa nenačíta z node_modules
**Riešenie:** Fallback na CDN sa spustí automaticky

### Problém 2: Oba zdroje zlyhajú
**Riešenie:** 
1. Skontrolujte internetové pripojenie
2. Reštartujte aplikáciu
3. Skúste `npm install jspdf --force`

### Problém 3: PDF sa vytvorí, ale nestiahne
**Riešenie:** Problém s Electron file system
```javascript
// V main.js pridajte:
webPreferences: {
    webSecurity: false,
    allowRunningInsecureContent: true
}
```

### Problém 4: Chyba "Cannot read property 'jsPDF'"
**Riešenie:** Knižnica sa nenačítala včas
```javascript
// Pridajte delay pred generovaním:
setTimeout(() => generatePDF(), 100);
```

## 📝 Súbory upravené

1. **index.html** - Pridaný fallback mechanizmus pre jsPDF
2. **script.js** - Vylepšené error handling a diagnostika
3. **main.js** - Pridané test menu
4. **test-pdf.html** - Samostatný test súbor
5. **package.json** - Pridaná jsPDF závislosť

## ✅ Overenie funkcionality

Po implementácii týchto zmien by PDF generovanie malo fungovať. Ak stále nefunguje:

1. **Reštartujte aplikáciu** úplne
2. **Vyčistite cache**: `rm -rf node_modules && npm install`
3. **Skúste test menu** pre izolované testovanie
4. **Skontrolujte console** pre chybové hlásenia

## 🎯 Výsledok

- ✅ PDF generovanie funguje v development režime (`npm start`)
- ✅ PDF generovanie funguje v zabalených aplikáciách
- ✅ Automatický fallback na CDN ak lokálna verzia zlyhá
- ✅ Detailné error reporting pre diagnostiku
- ✅ Test funkcionalita pre overenie

---

**Poznámka:** Ak problém pretrváva, skúste spustiť `npm start` a použiť test menu na izolované testovanie PDF funkcionality.
