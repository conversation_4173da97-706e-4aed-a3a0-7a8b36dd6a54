// Orders Management System
// Data structures and functionality for managing orders and tasks

// Global data storage
let orders = [];
let tasks = [];
let currentDate = new Date();

// Task status definitions
const TASK_STATUS = {
    PENDING: 'pending',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled',
    ARCHIVED: 'archived'
};

// Task lifecycle steps
const TASK_STEPS = {
    CREATED: 'created',
    SCHEDULED: 'scheduled',
    ARRIVED: 'arrived',
    PHOTO_BEFORE: 'photo_before',
    CLEANING: 'cleaning',
    PHOTO_AFTER: 'photo_after',
    FINISHED: 'finished',
    PAID: 'paid'
};

// Package definitions for automatic task generation
const packageDefinitions = {
    sviatocny_urnove: {
        name: 'Sviatočný - Urnové miesto',
        price: 90,
        tasks: [
            { offset: 30, type: 'základná', description: 'Základ<PERSON> údržba' },
            { date: 'easter', type: 'základ<PERSON>', description: 'Veľkonočné čistenie' },
            { date: '11-01', type: 'základ<PERSON>', description: 'Sviatok všetkých svätých' },
            { date: '12-24', type: 'základná', description: 'Vianočné čistenie' }
        ]
    },
    sviatocny_jednohrob: {
        name: 'Sviatočný - Jednohrob',
        price: 208,
        tasks: [
            { offset: 30, type: 'základná', description: 'Základná údržba' },
            { date: 'easter', type: 'základná', description: 'Veľkonočné čistenie' },
            { date: '11-01', type: 'základná', description: 'Sviatok všetkých svätých' },
            { date: '12-24', type: 'základná', description: 'Vianočné čistenie' }
        ]
    },
    sviatocny_dvojhrob: {
        name: 'Sviatočný - Dvojhrob',
        price: 300,
        tasks: [
            { offset: 30, type: 'základná', description: 'Základná údržba' },
            { date: 'easter', type: 'základná', description: 'Veľkonočné čistenie' },
            { date: '11-01', type: 'základná', description: 'Sviatok všetkých svätých' },
            { date: '12-24', type: 'základná', description: 'Vianočné čistenie' }
        ]
    },
    celorocny_urnove: {
        name: 'Celoročný Premium - Urnové miesto',
        price: 354,
        tasks: 'monthly' // Generate monthly tasks
    },
    celorocny_jednohrob: {
        name: 'Celoročný Premium - Jednohrob',
        price: 806,
        tasks: 'monthly'
    },
    celorocny_dvojhrob: {
        name: 'Celoročný Premium - Dvojhrob',
        price: 1036,
        tasks: 'monthly'
    }
};

// Initialize orders management
document.addEventListener('DOMContentLoaded', function() {
    initializeOrdersSystem();
    loadOrdersData();
    updateDashboard();
    updateTodayDate();

    // Initialize cleaning system if it exists
    if (window.cleaningSystem) {
        window.cleaningSystem.updateCleaningDashboard();
    }
});

function initializeOrdersSystem() {
    // Tab navigation
    const navTabs = document.querySelectorAll('.nav-tab');
    navTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            switchTab(tabName);
        });
    });

    // Add order form
    const addOrderForm = document.getElementById('addOrderForm');
    if (addOrderForm) {
        addOrderForm.addEventListener('submit', handleAddOrder);
    }

    // Initialize cleaning system navigation if it exists
    const cleaningNavTabs = document.querySelectorAll('.cleaning-nav-tab');
    if (cleaningNavTabs.length > 0 && window.cleaningSystem) {
        cleaningNavTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const viewName = this.dataset.cleaningView;
                window.cleaningSystem.switchCleaningView(viewName);
            });
        });
    }

    // Edit order form
    const editOrderForm = document.getElementById('editOrderForm');
    if (editOrderForm) {
        editOrderForm.addEventListener('submit', handleEditOrder);
    }

    // Orders search
    const ordersSearch = document.getElementById('ordersSearch');
    if (ordersSearch) {
        ordersSearch.addEventListener('input', filterOrders);
    }

    // Filters
    const filters = ['searchTasks', 'dateFilter', 'locationFilter', 'typeFilter'];
    filters.forEach(filterId => {
        const element = document.getElementById(filterId);
        if (element) {
            element.addEventListener('change', filterTasks);
            element.addEventListener('input', filterTasks);
        }
    });
}

function switchTab(tabName) {
    // Update nav tabs
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    const activeTab = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeTab) {
        activeTab.classList.add('active');
    }

    // Update tab panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('active');
    });
    const activePane = document.getElementById(`${tabName}-tab`);
    if (activePane) {
        activePane.classList.add('active');
    }

    // Update content based on active tab
    switch(tabName) {
        case 'orders':
            updateDashboard();
            renderTasks();
            break;
        case 'clients':
            if (typeof updateClientsDisplay === 'function') {
                updateClientsDisplay();
                updateCRMStats();
            }
            break;
        case 'invoices':
            if (typeof updateInvoicesDisplay === 'function') {
                updateInvoicesDisplay();
                updateCRMStats();
            }
            break;
        case 'services':
            if (typeof updateServicesDisplay === 'function') {
                updateServicesDisplay();
            }
            break;
    }
}

function updateTodayDate() {
    const today = new Date();
    const options = { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    const todayElement = document.getElementById('todayDate');
    if (todayElement) {
        todayElement.textContent = today.toLocaleDateString('sk-SK', options);
    }
}

function updateDashboard() {
    const activeContracts = orders.filter(order => order.status === 'active').length;
    const monthlyCleanings = tasks.filter(task => {
        const taskDate = new Date(task.date);
        const now = new Date();
        return taskDate.getMonth() === now.getMonth() &&
               taskDate.getFullYear() === now.getFullYear();
    }).length;
    const pendingTasks = tasks.filter(task => task.status === 'pending').length;
    const completedToday = tasks.filter(task => {
        const taskDate = new Date(task.date);
        const today = new Date();
        return task.status === 'completed' &&
               taskDate.toDateString() === today.toDateString();
    }).length;

    // Calculate revenue and discounts
    let totalRevenue = 0;
    let totalDiscounts = 0;

    orders.forEach(order => {
        if (order.quoteData) {
            totalRevenue += order.quoteData.total || 0;
            totalDiscounts += order.quoteData.discountAmount || 0;
        }
    });

    // Calculate pending invoices
    const pendingInvoiceOrders = showPendingInvoiceOrders();
    const pendingInvoicesCount = pendingInvoiceOrders.length;

    // Calculate total remaining cleanings
    const totalRemainingCleanings = orders.reduce((total, order) => {
        return total + (order.remainingCleanings || 0);
    }, 0);

    // Update dashboard cards
    updateDashboardCard('activeContracts', activeContracts);
    updateDashboardCard('monthlyCleanings', monthlyCleanings);
    updateDashboardCard('pendingTasks', pendingTasks);
    updateDashboardCard('completedToday', completedToday);
    updateDashboardCard('totalRevenue', formatPrice(totalRevenue));
    updateDashboardCard('totalDiscounts', formatPrice(totalDiscounts));
    updateDashboardCard('pendingInvoices', pendingInvoicesCount);
    updateDashboardCard('totalRemainingCleanings', totalRemainingCleanings);
}

function updateDashboardCard(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
    }
}

function formatPrice(price) {
    return new Intl.NumberFormat('sk-SK', {
        style: 'currency',
        currency: 'EUR',
        minimumFractionDigits: 2
    }).format(price);
}

function generateOrderId() {
    return 'ORD-' + Date.now().toString().slice(-6);
}

function generateTaskId() {
    return 'TASK-' + Date.now().toString().slice(-6) + '-' + Math.random().toString(36).substr(2, 3);
}

function calculateEasterDate(year) {
    // Easter calculation algorithm
    const a = year % 19;
    const b = Math.floor(year / 100);
    const c = year % 100;
    const d = Math.floor(b / 4);
    const e = b % 4;
    const f = Math.floor((b + 8) / 25);
    const g = Math.floor((b - f + 1) / 3);
    const h = (19 * a + b - d - g + 15) % 30;
    const i = Math.floor(c / 4);
    const k = c % 4;
    const l = (32 + 2 * e + 2 * i - h - k) % 7;
    const m = Math.floor((a + 11 * h + 22 * l) / 451);
    const month = Math.floor((h + l - 7 * m + 114) / 31);
    const day = ((h + l - 7 * m + 114) % 31) + 1;
    return new Date(year, month - 1, day);
}

function generateTasks(packageType, startDate, orderId) {
    const packageDef = packageDefinitions[packageType];
    if (!packageDef) return [];

    const generatedTasks = [];
    const start = new Date(startDate);
    const year = start.getFullYear();

    if (packageDef.tasks === 'monthly') {
        // Generate monthly tasks for a year
        for (let i = 0; i < 12; i++) {
            const taskDate = new Date(start);
            taskDate.setMonth(start.getMonth() + i);
            
            generatedTasks.push({
                id: generateTaskId(),
                orderId: orderId,
                date: taskDate.toISOString().split('T')[0],
                time: '09:00',
                type: 'základná',
                description: 'Mesačná údržba',
                status: TASK_STATUS.PENDING,
                steps: {
                    created: new Date().toISOString(),
                    scheduled: taskDate.toISOString(),
                    arrived: null,
                    photo_before: null,
                    cleaning: null,
                    photo_after: null,
                    finished: null,
                    paid: null
                },
                location: {
                    cemetery: '',
                    sector: '',
                    coordinates: null
                },
                photos: {
                    before: [],
                    after: []
                },
                notes: '',
                duration: null,
                price: 29,
                paid: false,
                assignedTo: null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });
        }
    } else {
        // Generate specific tasks
        packageDef.tasks.forEach(taskDef => {
            let taskDate;
            
            if (taskDef.offset) {
                taskDate = new Date(start);
                taskDate.setDate(taskDate.getDate() + taskDef.offset);
            } else if (taskDef.date === 'easter') {
                taskDate = calculateEasterDate(year);
            } else {
                const [month, day] = taskDef.date.split('-');
                taskDate = new Date(year, parseInt(month) - 1, parseInt(day));
            }

            generatedTasks.push({
                id: generateTaskId(),
                orderId: orderId,
                date: taskDate.toISOString().split('T')[0],
                time: '09:00',
                type: taskDef.type,
                description: taskDef.description,
                status: TASK_STATUS.PENDING,
                steps: {
                    created: new Date().toISOString(),
                    scheduled: taskDate.toISOString(),
                    arrived: null,
                    photo_before: null,
                    cleaning: null,
                    photo_after: null,
                    finished: null,
                    paid: null
                },
                location: {
                    cemetery: '',
                    sector: '',
                    coordinates: null
                },
                photos: {
                    before: [],
                    after: []
                },
                notes: '',
                duration: null,
                price: getTaskPrice(taskDef.type),
                paid: false,
                assignedTo: null,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            });
        });
    }

    return generatedTasks;
}

function createOrderFromQuote() {
    if (selectedServices.length === 0) {
        alert('Najprv vyberte služby v cenovej ponuke.');
        return;
    }

    if (!validateCustomerData()) {
        alert('Najprv vyplňte údaje o zákazníkovi.');
        return;
    }

    // Switch to orders tab and show add order modal with pre-filled data
    switchTab('orders');
    
    // Pre-fill the form with customer data
    document.getElementById('orderCustomerName').value = customerData.name || '';
    document.getElementById('orderCustomerPhone').value = customerData.phone || '';
    document.getElementById('orderCustomerEmail').value = customerData.email || '';
    
    // Set default start date to today
    document.getElementById('orderStartDate').value = new Date().toISOString().split('T')[0];
    
    showAddOrderModal();
}

function showAddOrderModal() {
    const modal = document.getElementById('addOrderModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('show');
    modal.style.display = 'none';
}

function handleAddOrder(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    // Calculate remaining cleanings based on package type
    const packageDef = packageDefinitions[document.getElementById('orderPackage').value];
    let remainingCleanings = 0;

    if (packageDef) {
        if (packageDef.tasks === 'monthly') {
            remainingCleanings = 12; // 12 monthly cleanings per year
        } else if (packageDef.tasks === 'holiday') {
            remainingCleanings = 4; // 4 holiday cleanings per year
        } else if (packageDef.tasks === 'single') {
            remainingCleanings = 1; // Single cleaning
        }
    }

    // Allow manual override of remaining cleanings (for migrated clients)
    const manualCleanings = document.getElementById('orderRemainingCleanings')?.value;
    if (manualCleanings && !isNaN(parseInt(manualCleanings))) {
        remainingCleanings = parseInt(manualCleanings);
    }

    const orderData = {
        id: generateOrderId(),
        customer: {
            name: document.getElementById('orderCustomerName').value,
            phone: document.getElementById('orderCustomerPhone').value,
            email: document.getElementById('orderCustomerEmail').value
        },
        location: document.getElementById('orderLocation').value,
        package: document.getElementById('orderPackage').value,
        startDate: document.getElementById('orderStartDate').value,
        endDate: calculateEndDate(document.getElementById('orderStartDate').value),
        status: 'active',
        createdAt: new Date().toISOString(),
        // Cleaning tracking
        remainingCleanings: remainingCleanings,
        totalCleanings: remainingCleanings,
        completedCleanings: 0,
        // Include quote data if available
        quoteData: selectedServices && selectedServices.length > 0 ? {
            services: selectedServices.map(service => ({
                name: service.name,
                price: service.price,
                basePrice: service.basePrice,
                isCustom: service.isCustom
            })),
            discount: currentDiscount || 0,
            subtotal: selectedServices.reduce((sum, service) => sum + service.price, 0),
            discountAmount: selectedServices.reduce((sum, service) => sum + service.price, 0) * ((currentDiscount || 0) / 100),
            total: (selectedServices.reduce((sum, service) => sum + service.price, 0) * (1 - (currentDiscount || 0) / 100)) * 1.20
        } : null
    };

    // Generate tasks for this order
    const generatedTasks = generateTasks(orderData.package, orderData.startDate, orderData.id);
    
    // Add to storage
    orders.push(orderData);
    tasks.push(...generatedTasks);
    
    // Save to localStorage
    saveOrdersData();
    
    // Update UI
    updateDashboard();
    renderTasks();
    
    // Close modal
    closeModal('addOrderModal');
    
    // Reset form
    e.target.reset();
    
    alert(`Objednávka ${orderData.id} bola úspešne vytvorená s ${generatedTasks.length} úlohami.`);
}

function calculateEndDate(startDate) {
    const start = new Date(startDate);
    const end = new Date(start);
    end.setFullYear(end.getFullYear() + 1);
    return end.toISOString().split('T')[0];
}

function saveOrdersData() {
    localStorage.setItem('orders', JSON.stringify(orders));
    localStorage.setItem('tasks', JSON.stringify(tasks));

    // Sync with CRM system if available
    if (typeof window.syncCRMWithOrders === 'function') {
        setTimeout(() => {
            window.syncCRMWithOrders();
        }, 100);
    }
}

function loadOrdersData() {
    // Clear any existing data to start fresh
    localStorage.removeItem('orders');
    localStorage.removeItem('tasks');

    // Initialize with empty arrays
    orders = [];
    tasks = [];

    // Start with empty data - no automatic sample data creation
}

function createSampleData() {
    // Create sample orders
    const sampleOrders = [
        {
            id: 'ORD-001',
            customer: {
                name: 'Ján Novák',
                phone: '+421 901 234 567',
                email: '<EMAIL>'
            },
            location: 'bratislava',
            package: 'sviatocny_jednohrob',
            startDate: '2025-03-01',
            endDate: '2026-03-01',
            status: 'active',
            createdAt: new Date().toISOString()
        },
        {
            id: 'ORD-002',
            customer: {
                name: 'Mária Svoboda',
                phone: '+421 902 345 678',
                email: '<EMAIL>'
            },
            location: 'petrzalka',
            package: 'celorocny_urnove',
            startDate: '2025-02-15',
            endDate: '2026-02-15',
            status: 'active',
            createdAt: new Date().toISOString()
        }
    ];

    // Create sample tasks
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    const sampleTasks = [
        {
            id: 'TASK-001',
            orderId: 'ORD-001',
            date: today.toISOString().split('T')[0],
            time: '09:00',
            type: 'základná',
            description: 'Základná údržba',
            status: TASK_STATUS.PENDING,
            steps: {
                created: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
                scheduled: today.toISOString(),
                arrived: null,
                photo_before: null,
                cleaning: null,
                photo_after: null,
                finished: null,
                paid: null
            },
            location: {
                cemetery: 'Cintorín Bratislava',
                sector: 'A12',
                coordinates: null
            },
            photos: { before: [], after: [] },
            notes: '',
            duration: null,
            price: 29,
            paid: false,
            assignedTo: null,
            createdAt: new Date(Date.now() - 86400000).toISOString(),
            updatedAt: new Date().toISOString()
        },
        {
            id: 'TASK-002',
            orderId: 'ORD-002',
            date: today.toISOString().split('T')[0],
            time: '14:00',
            type: 'hĺbková',
            description: 'Hĺbkové čistenie',
            status: TASK_STATUS.IN_PROGRESS,
            steps: {
                created: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
                scheduled: today.toISOString(),
                arrived: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
                photo_before: new Date(Date.now() - 3300000).toISOString(), // 55 min ago
                cleaning: new Date(Date.now() - 3000000).toISOString(), // 50 min ago
                photo_after: null,
                finished: null,
                paid: null
            },
            location: {
                cemetery: 'Cintorín Petržalka',
                sector: 'B5',
                coordinates: null
            },
            photos: {
                before: [
                    { data: 'data:image/jpeg;base64,sample', timestamp: new Date(Date.now() - 3300000).toISOString() }
                ],
                after: []
            },
            notes: '',
            duration: null,
            price: 45,
            paid: false,
            assignedTo: null,
            createdAt: new Date(Date.now() - 172800000).toISOString(),
            updatedAt: new Date().toISOString()
        },
        {
            id: 'TASK-003',
            orderId: 'ORD-001',
            date: tomorrow.toISOString().split('T')[0],
            time: '10:30',
            type: 'základná',
            description: 'Základná údržba',
            status: TASK_STATUS.PENDING,
            steps: {
                created: new Date().toISOString(),
                scheduled: tomorrow.toISOString(),
                arrived: null,
                photo_before: null,
                cleaning: null,
                photo_after: null,
                finished: null,
                paid: null
            },
            location: {
                cemetery: 'Cintorín Bratislava',
                sector: 'C8',
                coordinates: null
            },
            photos: { before: [], after: [] },
            notes: '',
            duration: null,
            price: 29,
            paid: false,
            assignedTo: null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        },
        {
            id: 'TASK-004',
            orderId: 'ORD-002',
            date: new Date(Date.now() - 86400000).toISOString().split('T')[0], // yesterday
            time: '11:00',
            type: 'základná',
            description: 'Základná údržba',
            status: TASK_STATUS.COMPLETED,
            steps: {
                created: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
                scheduled: new Date(Date.now() - 86400000).toISOString(),
                arrived: new Date(Date.now() - 82800000).toISOString(),
                photo_before: new Date(Date.now() - 82500000).toISOString(),
                cleaning: new Date(Date.now() - 82200000).toISOString(),
                photo_after: new Date(Date.now() - 81000000).toISOString(),
                finished: new Date(Date.now() - 80700000).toISOString(),
                paid: new Date(Date.now() - 80400000).toISOString()
            },
            location: {
                cemetery: 'Cintorín Petržalka',
                sector: 'A3',
                coordinates: null
            },
            photos: {
                before: [
                    { data: 'data:image/jpeg;base64,sample', timestamp: new Date(Date.now() - 82500000).toISOString() }
                ],
                after: [
                    { data: 'data:image/jpeg;base64,sample', timestamp: new Date(Date.now() - 81000000).toISOString() }
                ]
            },
            notes: 'Odstránené staré kvety, umytý náhrobok, pridaný nový kahanec',
            duration: 45,
            price: 29,
            paid: true,
            assignedTo: null,
            createdAt: new Date(Date.now() - 259200000).toISOString(),
            updatedAt: new Date(Date.now() - 80400000).toISOString()
        }
    ];

    orders = sampleOrders;
    tasks = sampleTasks;

    saveOrdersData();
}

function clearAllData() {
    if (confirm('Naozaj chcete vymazať všetky dáta? Táto akcia sa nedá vrátiť späť.')) {
        orders = [];
        tasks = [];
        localStorage.removeItem('orders');
        localStorage.removeItem('tasks');
        updateDashboard();
        renderTasks();
        showNotification('Všetky dáta boli vymazané', 'info');
    }
}

function resetToSampleData() {
    if (confirm('Naozaj chcete obnoviť ukážkové dáta? Aktuálne dáta budú prepísané.')) {
        orders = [];
        tasks = [];
        createSampleData();
        updateDashboard();
        renderTasks();
        showNotification('Ukážkové dáta boli obnovené', 'success');
    }
}

function exportData() {
    const exportData = {
        orders: orders,
        tasks: tasks,
        exportDate: new Date().toISOString(),
        version: '1.0'
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `espomienka-data-${new Date().toISOString().split('T')[0]}.json`;
    link.click();

    showNotification('Dáta boli exportované', 'success');
}

function renderTasks() {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    // Filter tasks for today
    const todayTasks = tasks.filter(task => task.date === todayStr);

    // Filter upcoming tasks (next 7 days)
    const upcomingTasks = tasks.filter(task => {
        const taskDate = new Date(task.date);
        const diffTime = taskDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays > 0 && diffDays <= 7;
    });

    // Render today's tasks
    renderTasksList('todayTasks', todayTasks);

    // Render upcoming tasks
    renderTasksList('upcomingTasks', upcomingTasks);
}

function renderTasksList(containerId, tasksList) {
    const container = document.getElementById(containerId);
    if (!container) return;

    if (tasksList.length === 0) {
        container.innerHTML = `
            <div class="no-tasks">
                <div class="no-tasks-content">
                    <i class="fas fa-calendar-times"></i>
                    <h4>Žiadne úlohy</h4>
                    <p>Vytvorte objednávku pre generovanie úloh</p>
                    <button class="btn btn-primary" onclick="showAddOrderModal()">
                        <i class="fas fa-plus"></i> Pridať objednávku
                    </button>
                </div>
            </div>
        `;
        return;
    }

    const tasksHTML = tasksList.map(task => {
        const order = orders.find(o => o.id === task.orderId);
        const customerName = order ? order.customer.name : 'Neznámy zákazník';
        const location = order ? getLocationName(order.location) : 'Neznáma lokalita';

        const progress = getTaskProgress(task);
        const statusIcon = getTaskStatusIcon(task.status);
        const statusText = getTaskStatusText(task.status);

        return `
            <div class="task-item task-${task.status}" data-task-id="${task.id}">
                <div class="task-status-indicator">
                    <span class="status-icon">${statusIcon}</span>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                </div>

                <div class="task-content">
                    <div class="task-header">
                        <div class="task-title">${customerName} - ${getPackageName(order?.package)}</div>
                        <div class="task-status">${statusText}</div>
                    </div>

                    <div class="task-details">
                        <div class="task-detail">
                            <i class="fas fa-calendar"></i>
                            ${formatDate(task.date)} ${task.time || ''}
                        </div>
                        <div class="task-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            ${location} ${task.location?.sector ? `- ${task.location.sector}` : ''}
                        </div>
                        <div class="task-detail">
                            <i class="fas fa-clipboard-list"></i>
                            ${task.description}
                        </div>
                        <div class="task-detail">
                            <i class="fas fa-euro-sign"></i>
                            ${task.price} EUR ${task.paid ? '✅' : '⏳'}
                            ${task.invoiced ? '<span class="invoiced-badge"><i class="fas fa-file-invoice"></i> Vyfakturované</span>' : ''}
                        </div>
                    </div>

                    ${task.status === TASK_STATUS.IN_PROGRESS ? `
                        <div class="task-progress-steps">
                            <div class="step ${task.steps.arrived ? 'completed' : ''}">
                                <i class="fas fa-map-marker-alt"></i> Prišiel na miesto
                            </div>
                            <div class="step ${task.steps.photo_before ? 'completed' : ''}">
                                <i class="fas fa-camera"></i> Foto pred (${task.photos.before.length})
                            </div>
                            <div class="step ${task.steps.cleaning ? 'completed' : ''}">
                                <i class="fas fa-broom"></i> Čistenie
                            </div>
                            <div class="step ${task.steps.photo_after ? 'completed' : ''}">
                                <i class="fas fa-camera"></i> Foto po (${task.photos.after.length})
                            </div>
                        </div>
                    ` : ''}
                </div>

                <div class="task-actions">
                    ${getTaskActionButtons(task)}
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = tasksHTML;
}

function getLocationName(locationCode) {
    const locations = {
        'bratislava': 'Cintorín Bratislava',
        'petrzalka': 'Cintorín Petržalka'
    };
    return locations[locationCode] || locationCode;
}

function getPackageName(packageCode) {
    const packageDef = packageDefinitions[packageCode];
    return packageDef ? packageDef.name : packageCode;
}

function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleDateString('sk-SK', {
        day: 'numeric',
        month: 'short'
    });
}

function toggleTaskStatus(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.status = task.status === 'completed' ? 'pending' : 'completed';
        if (task.status === 'completed') {
            task.completedAt = new Date().toISOString();
        } else {
            delete task.completedAt;
        }

        saveOrdersData();
        updateDashboard();
        renderTasks();
    }
}

function showTaskDetail(taskId) {
    const task = tasks.find(t => t.id === taskId);
    const order = orders.find(o => o.id === task.orderId);

    if (!task || !order) return;

    const detailHTML = `
        <div class="task-detail-content">
            <h4>${order.customer.name} - ${getPackageName(order.package)}</h4>
            <div class="detail-grid">
                <div class="detail-item">
                    <strong>Dátum:</strong> ${new Date(task.date).toLocaleDateString('sk-SK')}
                </div>
                <div class="detail-item">
                    <strong>Typ:</strong> ${task.description}
                </div>
                <div class="detail-item">
                    <strong>Lokalita:</strong> ${getLocationName(order.location)}
                </div>
                <div class="detail-item">
                    <strong>Status:</strong> ${task.status === 'completed' ? 'Dokončené' : 'Čakajúce'}
                </div>
                <div class="detail-item">
                    <strong>Zákazník:</strong> ${order.customer.name}
                </div>
                <div class="detail-item">
                    <strong>Telefón:</strong> ${order.customer.phone}
                </div>
                <div class="detail-item">
                    <strong>Email:</strong> ${order.customer.email}
                </div>
            </div>
            ${task.notes ? `<div class="task-notes"><strong>Poznámky:</strong><br>${task.notes}</div>` : ''}
        </div>
    `;

    document.getElementById('taskDetailContent').innerHTML = detailHTML;
    showModal('taskDetailModal');
}

function showModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.add('show');
    modal.style.display = 'flex';
}

function filterTasks() {
    const searchTerm = document.getElementById('searchTasks')?.value.toLowerCase() || '';
    const dateFilter = document.getElementById('dateFilter')?.value || 'all';
    const locationFilter = document.getElementById('locationFilter')?.value || 'all';
    const typeFilter = document.getElementById('typeFilter')?.value || 'all';

    let filteredTasks = tasks.filter(task => {
        const order = orders.find(o => o.id === task.orderId);
        if (!order) return false;

        // Search filter
        if (searchTerm && !order.customer.name.toLowerCase().includes(searchTerm)) {
            return false;
        }

        // Location filter
        if (locationFilter !== 'all' && order.location !== locationFilter) {
            return false;
        }

        // Type filter
        if (typeFilter !== 'all') {
            if (typeFilter === 'basic' && !task.description.toLowerCase().includes('základná')) {
                return false;
            }
            if (typeFilter === 'deep' && !task.description.toLowerCase().includes('hĺbková')) {
                return false;
            }
        }

        // Date filter
        const taskDate = new Date(task.date);
        const today = new Date();

        switch (dateFilter) {
            case 'today':
                return taskDate.toDateString() === today.toDateString();
            case 'week':
                const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
                return taskDate >= today && taskDate <= weekFromNow;
            case 'month':
                return taskDate.getMonth() === today.getMonth() &&
                       taskDate.getFullYear() === today.getFullYear();
            default:
                return true;
        }
    });

    // Re-render with filtered tasks
    const todayTasks = filteredTasks.filter(task => {
        const taskDate = new Date(task.date);
        const today = new Date();
        return taskDate.toDateString() === today.toDateString();
    });

    const upcomingTasks = filteredTasks.filter(task => {
        const taskDate = new Date(task.date);
        const today = new Date();
        const diffTime = taskDate.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays > 0 && diffDays <= 7;
    });

    renderTasksList('todayTasks', todayTasks);
    renderTasksList('upcomingTasks', upcomingTasks);
}

function showCalendarView() {
    generateCalendar();
    showModal('calendarModal');
}

function generateCalendar() {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Update calendar title
    const monthNames = [
        'Január', 'Február', 'Marec', 'Apríl', 'Máj', 'Jún',
        'Júl', 'August', 'September', 'Október', 'November', 'December'
    ];
    document.getElementById('calendarTitle').textContent = `${monthNames[month]} ${year}`;

    // Generate calendar grid
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay() + 1); // Start from Monday

    const calendarHTML = [];

    // Add day headers
    const dayHeaders = ['Po', 'Ut', 'St', 'Št', 'Pi', 'So', 'Ne'];
    dayHeaders.forEach(day => {
        calendarHTML.push(`<div class="calendar-day-header">${day}</div>`);
    });

    // Add calendar days
    const currentDate = new Date(startDate);
    for (let i = 0; i < 42; i++) { // 6 weeks
        const isCurrentMonth = currentDate.getMonth() === month;
        const isToday = currentDate.toDateString() === new Date().toDateString();
        const dateStr = currentDate.toISOString().split('T')[0];

        // Find tasks for this day
        const dayTasks = tasks.filter(task => task.date === dateStr);

        let dayClass = 'calendar-day';
        if (!isCurrentMonth) dayClass += ' other-month';
        if (isToday) dayClass += ' today';
        if (dayTasks.length > 0) dayClass += ' has-tasks';

        const tasksHTML = dayTasks.slice(0, 3).map(task => {
            const order = orders.find(o => o.id === task.orderId);
            const customerName = order ? order.customer.name.split(' ')[0] : 'Neznámy';
            return `<div class="calendar-task">${customerName}</div>`;
        }).join('');

        calendarHTML.push(`
            <div class="${dayClass}">
                <div class="calendar-day-number">${currentDate.getDate()}</div>
                ${tasksHTML}
                ${dayTasks.length > 3 ? `<div class="calendar-task">+${dayTasks.length - 3}</div>` : ''}
            </div>
        `);

        currentDate.setDate(currentDate.getDate() + 1);
    }

    document.getElementById('calendarGrid').innerHTML = calendarHTML.join('');
}

function previousMonth() {
    currentDate.setMonth(currentDate.getMonth() - 1);
    generateCalendar();
}

function nextMonth() {
    currentDate.setMonth(currentDate.getMonth() + 1);
    generateCalendar();
}

// Task lifecycle management functions
function getTaskPrice(type) {
    const prices = {
        'základná': 29,
        'hĺbková': 45,
        'kompletná': 65
    };
    return prices[type] || 29;
}

function startTask(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (task && task.status === TASK_STATUS.PENDING) {
        task.status = TASK_STATUS.IN_PROGRESS;
        task.steps.arrived = new Date().toISOString();
        task.updatedAt = new Date().toISOString();

        saveOrdersData();
        updateDashboard();
        renderTasks();

        // Show task progress modal
        showTaskProgressModal(taskId);
    }
}

function addPhotoBefore(taskId, photoData) {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.photos.before.push({
            data: photoData,
            timestamp: new Date().toISOString()
        });
        task.steps.photo_before = new Date().toISOString();
        task.updatedAt = new Date().toISOString();

        saveOrdersData();
        updateTaskProgress(taskId);
    }
}

function startCleaning(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.steps.cleaning = new Date().toISOString();
        task.updatedAt = new Date().toISOString();

        saveOrdersData();
        updateTaskProgress(taskId);
    }
}

function addPhotoAfter(taskId, photoData) {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.photos.after.push({
            data: photoData,
            timestamp: new Date().toISOString()
        });
        task.steps.photo_after = new Date().toISOString();
        task.updatedAt = new Date().toISOString();

        saveOrdersData();
        updateTaskProgress(taskId);
    }
}

function completeTask(taskId, notes = '') {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.status = TASK_STATUS.COMPLETED;
        task.steps.finished = new Date().toISOString();
        task.notes = notes;
        task.updatedAt = new Date().toISOString();

        // Calculate duration
        if (task.steps.arrived && task.steps.finished) {
            const start = new Date(task.steps.arrived);
            const end = new Date(task.steps.finished);
            task.duration = Math.round((end - start) / (1000 * 60)); // minutes
        }

        // Update remaining cleanings for the order
        const order = orders.find(o => o.id === task.orderId);
        if (order && order.remainingCleanings !== undefined && order.remainingCleanings > 0) {
            order.remainingCleanings--;
            order.completedCleanings = (order.completedCleanings || 0) + 1;
            order.updatedAt = new Date().toISOString();
        }

        saveOrdersData();
        updateDashboard();
        renderTasks();
        closeModal('taskProgressModal');

        // Show completion confirmation
        showTaskCompletionModal(taskId);
    }
}

function markTaskPaid(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.paid = true;
        task.steps.paid = new Date().toISOString();
        task.updatedAt = new Date().toISOString();

        saveOrdersData();
        updateDashboard();
        renderTasks();
    }
}

function cancelTask(taskId, reason = '') {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.status = TASK_STATUS.CANCELLED;
        task.notes = reason;
        task.updatedAt = new Date().toISOString();

        saveOrdersData();
        updateDashboard();
        renderTasks();
    }
}

function archiveTask(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (task && task.status === TASK_STATUS.COMPLETED) {
        task.status = TASK_STATUS.ARCHIVED;
        task.updatedAt = new Date().toISOString();

        saveOrdersData();
        updateDashboard();
        renderTasks();
    }
}

function getTaskStatusIcon(status) {
    const icons = {
        [TASK_STATUS.PENDING]: '⏸️',
        [TASK_STATUS.IN_PROGRESS]: '🔄',
        [TASK_STATUS.COMPLETED]: '✅',
        [TASK_STATUS.CANCELLED]: '❌',
        [TASK_STATUS.ARCHIVED]: '📦'
    };
    return icons[status] || '❓';
}

function getTaskStatusText(status) {
    const texts = {
        [TASK_STATUS.PENDING]: 'Naplánované',
        [TASK_STATUS.IN_PROGRESS]: 'Prebieha',
        [TASK_STATUS.COMPLETED]: 'Dokončené',
        [TASK_STATUS.CANCELLED]: 'Zrušené',
        [TASK_STATUS.ARCHIVED]: 'Archivované'
    };
    return texts[status] || 'Neznámy';
}

function getTaskProgress(task) {
    const steps = task.steps;
    let completed = 0;
    let total = 8;

    if (steps.created) completed++;
    if (steps.scheduled) completed++;
    if (steps.arrived) completed++;
    if (steps.photo_before) completed++;
    if (steps.cleaning) completed++;
    if (steps.photo_after) completed++;
    if (steps.finished) completed++;
    if (steps.paid) completed++;

    return Math.round((completed / total) * 100);
}

function getTaskActionButtons(task) {
    switch (task.status) {
        case TASK_STATUS.PENDING:
            return `
                <button class="btn btn-primary" onclick="startTask('${task.id}')">
                    <i class="fas fa-play"></i> Začať
                </button>
                <button class="btn btn-secondary" onclick="editTask('${task.id}')">
                    <i class="fas fa-edit"></i> Upraviť
                </button>
                <button class="btn btn-danger" onclick="cancelTask('${task.id}')">
                    <i class="fas fa-times"></i> Zrušiť
                </button>
            `;

        case TASK_STATUS.IN_PROGRESS:
            return `
                <button class="btn btn-success" onclick="showTaskProgressModal('${task.id}')">
                    <i class="fas fa-tasks"></i> Pokračovať
                </button>
                <button class="btn btn-secondary" onclick="showTaskDetail('${task.id}')">
                    <i class="fas fa-info"></i> Detail
                </button>
            `;

        case TASK_STATUS.COMPLETED:
            return `
                <button class="btn btn-secondary" onclick="showTaskDetail('${task.id}')">
                    <i class="fas fa-info"></i> Detail
                </button>
                ${!task.invoiced ? `
                    <button class="btn btn-primary" onclick="createInvoiceFromTask('${task.id}')">
                        <i class="fas fa-file-invoice"></i> Vytvoriť faktúru
                    </button>
                ` : ''}
                ${!task.paid ? `
                    <button class="btn btn-success" onclick="markTaskPaid('${task.id}')">
                        <i class="fas fa-euro-sign"></i> Zaplatené
                    </button>
                ` : ''}
                <button class="btn btn-primary" onclick="scheduleNextTask('${task.id}')">
                    <i class="fas fa-redo"></i> Naplánovať ďalšie
                </button>
            `;

        case TASK_STATUS.CANCELLED:
            return `
                <button class="btn btn-secondary" onclick="showTaskDetail('${task.id}')">
                    <i class="fas fa-info"></i> Detail
                </button>
                <button class="btn btn-primary" onclick="rescheduleTask('${task.id}')">
                    <i class="fas fa-calendar-plus"></i> Naplánovať znovu
                </button>
            `;

        default:
            return `
                <button class="btn btn-secondary" onclick="showTaskDetail('${task.id}')">
                    <i class="fas fa-info"></i> Detail
                </button>
            `;
    }
}

function showTaskProgressModal(taskId) {
    const task = tasks.find(t => t.id === taskId);
    const order = orders.find(o => o.id === task.orderId);

    if (!task || !order) return;

    const modalHTML = `
        <div class="task-progress-content">
            <div class="task-info">
                <h4>${order.customer.name} - ${getPackageName(order.package)}</h4>
                <p><i class="fas fa-map-marker-alt"></i> ${getLocationName(order.location)}</p>
                <p><i class="fas fa-clock"></i> Začaté: ${new Date(task.steps.arrived).toLocaleTimeString('sk-SK')}</p>
            </div>

            <div class="progress-steps">
                <div class="step-item ${task.steps.arrived ? 'completed' : 'active'}">
                    <div class="step-icon"><i class="fas fa-map-marker-alt"></i></div>
                    <div class="step-content">
                        <h5>Prišiel na miesto</h5>
                        <p>Potvrďte príchod na cintorín</p>
                        ${!task.steps.arrived ? `
                            <button class="btn btn-primary" onclick="confirmArrival('${task.id}')">
                                Potvrdiť príchod
                            </button>
                        ` : `<span class="completed-time">✅ ${new Date(task.steps.arrived).toLocaleTimeString('sk-SK')}</span>`}
                    </div>
                </div>

                <div class="step-item ${task.steps.photo_before ? 'completed' : (task.steps.arrived ? 'active' : '')}">
                    <div class="step-icon"><i class="fas fa-camera"></i></div>
                    <div class="step-content">
                        <h5>Foto pred čistením</h5>
                        <p>Odfotografujte stav pred začatím práce</p>
                        ${task.steps.arrived && !task.steps.photo_before ? `
                            <button class="btn btn-primary" onclick="takePhotoBefore('${task.id}')">
                                <i class="fas fa-camera"></i> Odfotiť
                            </button>
                        ` : task.steps.photo_before ? `
                            <span class="completed-time">✅ ${task.photos.before.length} foto(s)</span>
                            <button class="btn btn-secondary" onclick="viewPhotos('${task.id}', 'before')">
                                Zobraziť
                            </button>
                        ` : ''}
                    </div>
                </div>

                <div class="step-item ${task.steps.cleaning ? 'completed' : (task.steps.photo_before ? 'active' : '')}">
                    <div class="step-icon"><i class="fas fa-broom"></i></div>
                    <div class="step-content">
                        <h5>Čistenie</h5>
                        <p>Vykonajte čistenie podľa typu úlohy</p>
                        ${task.steps.photo_before && !task.steps.cleaning ? `
                            <button class="btn btn-primary" onclick="startCleaning('${task.id}')">
                                <i class="fas fa-play"></i> Začať čistenie
                            </button>
                        ` : task.steps.cleaning ? `
                            <span class="completed-time">✅ ${new Date(task.steps.cleaning).toLocaleTimeString('sk-SK')}</span>
                        ` : ''}
                    </div>
                </div>

                <div class="step-item ${task.steps.photo_after ? 'completed' : (task.steps.cleaning ? 'active' : '')}">
                    <div class="step-icon"><i class="fas fa-camera"></i></div>
                    <div class="step-content">
                        <h5>Foto po čistení</h5>
                        <p>Odfotografujte výsledok práce</p>
                        ${task.steps.cleaning && !task.steps.photo_after ? `
                            <button class="btn btn-primary" onclick="takePhotoAfter('${task.id}')">
                                <i class="fas fa-camera"></i> Odfotiť
                            </button>
                        ` : task.steps.photo_after ? `
                            <span class="completed-time">✅ ${task.photos.after.length} foto(s)</span>
                            <button class="btn btn-secondary" onclick="viewPhotos('${task.id}', 'after')">
                                Zobraziť
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>

            ${task.steps.photo_after ? `
                <div class="completion-section">
                    <h5>Dokončenie úlohy</h5>
                    <textarea id="taskNotes" placeholder="Poznámky k vykonanej práci..." rows="3">${task.notes}</textarea>
                    <button class="btn btn-success btn-large" onclick="completeTaskWithNotes('${task.id}')">
                        <i class="fas fa-check"></i> Dokončiť úlohu
                    </button>
                </div>
            ` : ''}
        </div>
    `;

    document.getElementById('taskProgressContent').innerHTML = modalHTML;
    showModal('taskProgressModal');
}

function updateTaskProgress(taskId) {
    // Refresh the progress modal if it's open
    const modal = document.getElementById('taskProgressModal');
    if (modal && modal.classList.contains('show')) {
        showTaskProgressModal(taskId);
    }

    // Update the main task list
    renderTasks();
}

// Individual task step functions
function confirmArrival(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.steps.arrived = new Date().toISOString();
        task.updatedAt = new Date().toISOString();

        saveOrdersData();
        updateTaskProgress(taskId);
    }
}

function takePhotoBefore(taskId) {
    // In a real app, this would open camera
    // For demo, we'll simulate adding a photo
    const photoData = `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=`;

    addPhotoBefore(taskId, photoData);

    // Show success message
    showNotification('Foto pred čistením bolo pridané', 'success');
}

function takePhotoAfter(taskId) {
    // In a real app, this would open camera
    // For demo, we'll simulate adding a photo
    const photoData = `data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=`;

    addPhotoAfter(taskId, photoData);

    // Show success message
    showNotification('Foto po čistení bolo pridané', 'success');
}

function completeTaskWithNotes(taskId) {
    const notes = document.getElementById('taskNotes')?.value || '';
    completeTask(taskId, notes);
}

function viewPhotos(taskId, type) {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;

    const photos = type === 'before' ? task.photos.before : task.photos.after;
    const title = type === 'before' ? 'Fotky pred čistením' : 'Fotky po čistení';

    const photosHTML = photos.map((photo, index) => `
        <div class="photo-item">
            <img src="${photo.data}" alt="${title} ${index + 1}" class="photo-preview">
            <p class="photo-timestamp">${new Date(photo.timestamp).toLocaleString('sk-SK')}</p>
        </div>
    `).join('');

    const modalHTML = `
        <div class="photos-viewer">
            <h4>${title}</h4>
            <div class="photos-grid">
                ${photosHTML}
            </div>
        </div>
    `;

    document.getElementById('photoViewerContent').innerHTML = modalHTML;
    showModal('photoViewerModal');
}

function scheduleNextTask(taskId) {
    const task = tasks.find(t => t.id === taskId);
    const order = orders.find(o => o.id === task.orderId);

    if (!task || !order) return;

    // Calculate next task date based on package type
    const currentDate = new Date(task.date);
    let nextDate = new Date(currentDate);

    if (order.package.includes('celorocny')) {
        // Monthly for yearly packages
        nextDate.setMonth(nextDate.getMonth() + 1);
    } else {
        // Quarterly for holiday packages
        nextDate.setMonth(nextDate.getMonth() + 3);
    }

    // Create new task
    const newTask = {
        ...task,
        id: generateTaskId(),
        date: nextDate.toISOString().split('T')[0],
        status: TASK_STATUS.PENDING,
        steps: {
            created: new Date().toISOString(),
            scheduled: nextDate.toISOString(),
            arrived: null,
            photo_before: null,
            cleaning: null,
            photo_after: null,
            finished: null,
            paid: null
        },
        photos: {
            before: [],
            after: []
        },
        notes: '',
        duration: null,
        paid: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };

    tasks.push(newTask);
    saveOrdersData();
    updateDashboard();
    renderTasks();

    showNotification(`Nová úloha naplánovaná na ${nextDate.toLocaleDateString('sk-SK')}`, 'success');
}

function rescheduleTask(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
        task.status = TASK_STATUS.PENDING;
        task.steps = {
            ...task.steps,
            arrived: null,
            photo_before: null,
            cleaning: null,
            photo_after: null,
            finished: null
        };
        task.photos = {
            before: [],
            after: []
        };
        task.updatedAt = new Date().toISOString();

        saveOrdersData();
        updateDashboard();
        renderTasks();

        showNotification('Úloha bola naplánovaná znovu', 'success');
    }
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check' : 'info'}-circle"></i>
            <span>${message}</span>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
    }, 3000);
}

function showTaskCompletionModal(taskId) {
    const task = tasks.find(t => t.id === taskId);
    const order = orders.find(o => o.id === task.orderId);

    if (!task || !order) return;

    const modalHTML = `
        <div class="completion-summary">
            <div class="completion-header">
                <i class="fas fa-check-circle completion-icon"></i>
                <h3>Úloha dokončená!</h3>
            </div>

            <div class="completion-details">
                <h4>${order.customer.name} - ${getPackageName(order.package)}</h4>
                <p><strong>Dokončené:</strong> ${new Date(task.steps.finished).toLocaleString('sk-SK')}</p>
                <p><strong>Trvanie:</strong> ${task.duration} minút</p>
                <p><strong>Cena:</strong> ${task.price} EUR</p>

                ${task.notes ? `
                    <div class="completion-notes">
                        <strong>Poznámky:</strong>
                        <p>${task.notes}</p>
                    </div>
                ` : ''}

                <div class="completion-photos">
                    <div class="photo-summary">
                        <span>📸 Fotky pred: ${task.photos.before.length}</span>
                        <span>📸 Fotky po: ${task.photos.after.length}</span>
                    </div>
                </div>
            </div>

            <div class="completion-actions">
                <button class="btn btn-primary" onclick="scheduleNextTask('${task.id}'); closeModal('taskCompletionModal');">
                    <i class="fas fa-calendar-plus"></i> Naplánovať ďalšie čistenie
                </button>
                <button class="btn btn-secondary" onclick="closeModal('taskCompletionModal')">
                    Zavrieť
                </button>
            </div>
        </div>
    `;

    document.getElementById('taskCompletionContent').innerHTML = modalHTML;
    showModal('taskCompletionModal');
}

// Orders Management Functions
function showOrdersListModal() {
    renderOrdersList();
    showModal('ordersListModal');
}

function renderOrdersList() {
    const container = document.getElementById('ordersListContent');
    if (!container) return;

    if (orders.length === 0) {
        container.innerHTML = `
            <div class="no-orders">
                <i class="fas fa-clipboard-list"></i>
                <h4>Žiadne objednávky</h4>
                <p>Zatiaľ neboli vytvorené žiadne objednávky</p>
                <button class="btn btn-primary" onclick="showAddOrderModal(); closeModal('ordersListModal')">
                    <i class="fas fa-plus"></i> Vytvoriť prvú objednávku
                </button>
            </div>
        `;
        return;
    }

    const ordersHTML = orders.map(order => {
        const orderTasks = tasks.filter(task => task.orderId === order.id);
        const completedTasks = orderTasks.filter(task => task.status === TASK_STATUS.COMPLETED).length;
        const totalTasks = orderTasks.length;

        return `
            <div class="order-item">
                <div class="order-header">
                    <div>
                        <div class="order-title">${order.customer.name}</div>
                        <div class="order-id">${order.id}</div>
                    </div>
                    <div class="order-status ${order.status}">${getOrderStatusText(order.status)}</div>
                </div>

                <div class="order-details">
                    <div class="order-detail">
                        <i class="fas fa-phone"></i>
                        ${order.customer.phone}
                    </div>
                    <div class="order-detail">
                        <i class="fas fa-envelope"></i>
                        ${order.customer.email}
                    </div>
                    <div class="order-detail">
                        <i class="fas fa-map-marker-alt"></i>
                        ${getLocationName(order.location)}
                    </div>
                    <div class="order-detail">
                        <i class="fas fa-box"></i>
                        ${getPackageName(order.package)}
                    </div>
                    <div class="order-detail">
                        <i class="fas fa-calendar"></i>
                        ${formatDate(order.startDate)} - ${formatDate(order.endDate)}
                    </div>
                    <div class="order-detail">
                        <i class="fas fa-tasks"></i>
                        ${completedTasks}/${totalTasks} úloh dokončených
                    </div>
                    ${order.remainingCleanings !== undefined ? `
                        <div class="order-detail">
                            <i class="fas fa-broom"></i>
                            ${order.remainingCleanings} zostávajúcich čistení
                        </div>
                    ` : ''}
                    ${order.quoteData ? `
                        <div class="order-detail">
                            <i class="fas fa-euro-sign"></i>
                            ${formatPrice(order.quoteData.total)} ${order.quoteData.discount > 0 ? `(zľava ${order.quoteData.discount}%)` : ''}
                        </div>
                    ` : ''}
                </div>

                <div class="order-actions">
                    <button class="btn btn-secondary" onclick="editOrder('${order.id}')">
                        <i class="fas fa-edit"></i> Upraviť
                    </button>
                    <button class="btn btn-info" onclick="viewOrderTasks('${order.id}')">
                        <i class="fas fa-tasks"></i> Úlohy (${totalTasks})
                    </button>
                    ${order.status === 'active' ? `
                        <button class="btn btn-warning" onclick="completeOrder('${order.id}')">
                            <i class="fas fa-check"></i> Dokončiť
                        </button>
                    ` : ''}
                    <button class="btn btn-danger" onclick="deleteOrder('${order.id}')">
                        <i class="fas fa-trash"></i> Vymazať
                    </button>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = ordersHTML;
}

function getOrderStatusText(status) {
    const statusTexts = {
        'active': 'Aktívna',
        'completed': 'Dokončená',
        'cancelled': 'Zrušená'
    };
    return statusTexts[status] || status;
}

function editOrder(orderId) {
    const order = orders.find(o => o.id === orderId);
    if (!order) return;

    // Fill the edit form
    document.getElementById('editOrderId').value = order.id;
    document.getElementById('editOrderCustomerName').value = order.customer.name;
    document.getElementById('editOrderCustomerPhone').value = order.customer.phone;
    document.getElementById('editOrderCustomerEmail').value = order.customer.email;
    document.getElementById('editOrderLocation').value = order.location;
    document.getElementById('editOrderPackage').value = order.package;
    document.getElementById('editOrderStartDate').value = order.startDate;
    document.getElementById('editOrderRemainingCleanings').value = order.remainingCleanings || 0;

    closeModal('ordersListModal');
    showModal('editOrderModal');
}

function deleteOrder(orderId) {
    const order = orders.find(o => o.id === orderId);
    if (!order) return;

    if (confirm(`Naozaj chcete vymazať objednávku ${order.id} pre ${order.customer.name}?\n\nTáto akcia vymaže aj všetky súvisiace úlohy a nedá sa vrátiť späť.`)) {
        // Remove order
        orders = orders.filter(o => o.id !== orderId);

        // Remove all related tasks
        tasks = tasks.filter(task => task.orderId !== orderId);

        // Save changes
        saveOrdersData();

        // Update UI
        updateDashboard();
        renderTasks();
        renderOrdersList();

        showNotification(`Objednávka ${order.id} bola vymazaná`, 'success');
    }
}

function completeOrder(orderId) {
    const order = orders.find(o => o.id === orderId);
    if (!order) return;

    if (confirm(`Naozaj chcete označiť objednávku ${order.id} ako dokončenú?`)) {
        order.status = 'completed';
        order.completedAt = new Date().toISOString();

        // Mark all pending tasks as cancelled
        tasks.forEach(task => {
            if (task.orderId === orderId && task.status === TASK_STATUS.PENDING) {
                task.status = TASK_STATUS.CANCELLED;
                task.notes = 'Objednávka bola dokončená';
                task.updatedAt = new Date().toISOString();
            }
        });

        saveOrdersData();
        updateDashboard();
        renderTasks();
        renderOrdersList();

        showNotification(`Objednávka ${order.id} bola označená ako dokončená`, 'success');
    }
}

function viewOrderTasks(orderId) {
    const orderTasks = tasks.filter(task => task.orderId === orderId);
    const order = orders.find(o => o.id === orderId);

    if (!order) return;

    closeModal('ordersListModal');

    // Filter tasks to show only this order's tasks
    const originalRenderTasks = window.renderTasks;
    window.renderTasks = function() {
        renderTasksList('todayTasks', orderTasks.filter(task => {
            const today = new Date().toISOString().split('T')[0];
            return task.date === today;
        }));

        renderTasksList('upcomingTasks', orderTasks.filter(task => {
            const today = new Date();
            const taskDate = new Date(task.date);
            const diffTime = taskDate.getTime() - today.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return diffDays > 0 && diffDays <= 7;
        }));
    };

    window.renderTasks();

    // Add a button to show all tasks again
    const ordersHeader = document.querySelector('.orders-header h3');
    if (ordersHeader) {
        ordersHeader.innerHTML = `
            <i class="fas fa-tasks"></i> Úlohy pre objednávku ${order.id} - ${order.customer.name}
            <button class="btn btn-secondary" onclick="showAllTasks()" style="margin-left: 1rem; font-size: 0.8rem;">
                <i class="fas fa-list"></i> Zobraziť všetky úlohy
            </button>
        `;
    }
}

function showAllTasks() {
    // Restore original renderTasks function
    location.reload(); // Simple solution to restore everything
}

function handleEditOrder(e) {
    e.preventDefault();

    const orderId = document.getElementById('editOrderId').value;
    const order = orders.find(o => o.id === orderId);
    if (!order) return;

    // Update order data
    order.customer.name = document.getElementById('editOrderCustomerName').value;
    order.customer.phone = document.getElementById('editOrderCustomerPhone').value;
    order.customer.email = document.getElementById('editOrderCustomerEmail').value;
    order.location = document.getElementById('editOrderLocation').value;

    // Update remaining cleanings
    const newRemainingCleanings = parseInt(document.getElementById('editOrderRemainingCleanings').value) || 0;
    order.remainingCleanings = newRemainingCleanings;

    const newPackage = document.getElementById('editOrderPackage').value;
    const newStartDate = document.getElementById('editOrderStartDate').value;

    // If package or start date changed, regenerate tasks
    if (order.package !== newPackage || order.startDate !== newStartDate) {
        if (confirm('Balík služieb alebo dátum začiatku sa zmenil. Chcete regenerovať úlohy? (Existujúce úlohy budú vymazané)')) {
            // Remove old tasks
            tasks = tasks.filter(task => task.orderId !== orderId);

            // Update order
            order.package = newPackage;
            order.startDate = newStartDate;
            order.endDate = calculateEndDate(newStartDate);

            // Generate new tasks
            const newTasks = generateTasks(newPackage, newStartDate, orderId);
            tasks.push(...newTasks);
        }
    }

    order.updatedAt = new Date().toISOString();

    // Save changes
    saveOrdersData();

    // Update UI
    updateDashboard();
    renderTasks();
    renderOrdersList();

    // Close modal
    closeModal('editOrderModal');

    showNotification(`Objednávka ${orderId} bola aktualizovaná`, 'success');
}

function editTask(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;

    const newDate = prompt('Zadajte nový dátum úlohy (YYYY-MM-DD):', task.date);
    if (newDate && newDate !== task.date) {
        // Validate date format
        if (!/^\d{4}-\d{2}-\d{2}$/.test(newDate)) {
            alert('Neplatný formát dátumu. Použite YYYY-MM-DD');
            return;
        }

        task.date = newDate;
        task.updatedAt = new Date().toISOString();

        saveOrdersData();
        updateDashboard();
        renderTasks();

        showNotification('Dátum úlohy bol zmenený', 'success');
    }
}

function filterOrders() {
    const searchTerm = document.getElementById('ordersSearch').value.toLowerCase();
    const orderItems = document.querySelectorAll('.order-item');

    orderItems.forEach(item => {
        const orderTitle = item.querySelector('.order-title').textContent.toLowerCase();
        const orderId = item.querySelector('.order-id').textContent.toLowerCase();
        const orderDetails = item.querySelector('.order-details').textContent.toLowerCase();

        const matches = orderTitle.includes(searchTerm) ||
                       orderId.includes(searchTerm) ||
                       orderDetails.includes(searchTerm);

        item.style.display = matches ? 'block' : 'none';
    });
}

// Integration with CRM/Invoicing system
function createInvoiceFromTask(taskId) {
    const task = tasks.find(t => t.id === taskId);
    const order = orders.find(o => o.id === task.orderId);

    if (!task || !order) {
        alert('Úloha alebo objednávka nebola nájdená');
        return;
    }

    if (task.status !== TASK_STATUS.COMPLETED) {
        alert('Faktúru možno vytvoriť len z dokončených úloh');
        return;
    }

    // Check if CRM system is available
    if (typeof createInvoiceFromOrder === 'function') {
        const invoiceId = createInvoiceFromOrder(order.id);
        if (invoiceId) {
            // Mark task as invoiced
            task.invoiced = true;
            task.invoiceId = invoiceId;

            // Mark order as invoiced if all tasks are invoiced
            const orderTasks = tasks.filter(t => t.orderId === order.id && t.status === TASK_STATUS.COMPLETED);
            const invoicedTasks = orderTasks.filter(t => t.invoiced);

            if (orderTasks.length === invoicedTasks.length) {
                order.invoiced = true;
            }

            saveOrdersData();
            renderTasks();

            // Switch to invoices tab
            switchTab('invoices');
        }
    } else {
        alert('CRM systém nie je dostupný. Faktúru vytvorte manuálne v sekcii Faktúry.');
    }
}

// Function to show orders that are ready for invoicing
function showPendingInvoiceOrders() {
    const completedOrders = orders.filter(order => {
        const orderTasks = tasks.filter(t => t.orderId === order.id);
        const completedTasks = orderTasks.filter(t => t.status === TASK_STATUS.COMPLETED);
        const invoicedTasks = completedTasks.filter(t => t.invoiced);

        // Order is ready for invoicing if it has completed tasks that are not invoiced
        return completedTasks.length > 0 && invoicedTasks.length < completedTasks.length;
    });

    return completedOrders;
}

// Function to get order total for invoicing
function getOrderInvoiceTotal(orderId) {
    const orderTasks = tasks.filter(t => t.orderId === orderId && t.status === TASK_STATUS.COMPLETED && !t.invoiced);
    return orderTasks.reduce((total, task) => total + (task.price || 0), 0);
}

// Function to create order from quote data
function createOrderFromQuoteData(quoteData) {
    if (!quoteData || !quoteData.customer || !quoteData.services) {
        alert('Neplatné údaje cenovej ponuky');
        return;
    }

    // Create new order
    const newOrder = {
        id: generateOrderId(),
        customer: quoteData.customer,
        services: quoteData.services,
        totalPrice: quoteData.total,
        discount: quoteData.discount || 0,
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        quoteData: quoteData // Store original quote data
    };

    // Add to orders array
    orders.push(newOrder);
    saveOrdersData();
    updateDashboard();

    // Show success message
    showNotification(`Objednávka ${newOrder.id} bola vytvorená z cenovej ponuky`, 'success');

    // Optionally show add order modal with pre-filled data
    showAddOrderModal();

    // Pre-fill the form
    document.getElementById('orderCustomerName').value = quoteData.customer.name;
    document.getElementById('orderCustomerPhone').value = quoteData.customer.phone;
    document.getElementById('orderCustomerEmail').value = quoteData.customer.email;

    // Set service description
    const serviceNames = quoteData.services.map(s => s.name).join(', ');
    document.getElementById('orderServiceDescription').value = serviceNames;
    document.getElementById('orderPrice').value = quoteData.total.toFixed(2);
}

// Export functions for global access
window.showAddOrderModal = showAddOrderModal;
window.closeModal = closeModal;
window.createOrderFromQuoteData = createOrderFromQuoteData;
