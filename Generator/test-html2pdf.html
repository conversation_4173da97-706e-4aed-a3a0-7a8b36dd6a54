<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test html2pdf.js</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #5e2e60;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #4a1f4a;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        #testContent {
            border: 2px dashed #ddd;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test html2pdf.js knižnice</h1>
        
        <div id="statusContainer">
            <div class="status warning">
                <i class="fas fa-clock"></i> Kontrolujem dostupnosť html2pdf.js...
            </div>
        </div>

        <div id="testContent">
            <h2>Testovací obsah pre PDF</h2>
            <p>Toto je testovací obsah, ktorý bude konvertovaný do PDF súboru.</p>
            <p><strong>Dátum testu:</strong> <span id="testDate"></span></p>
            <p><strong>Čas testu:</strong> <span id="testTime"></span></p>
            <p>Slovenské znaky: áäčďéíĺľňóôŕšťúýž ÁÄČĎÉÍĹĽŇÓÔŔŠŤÚÝŽ</p>
        </div>

        <div>
            <button id="testBtn" onclick="testPDFGeneration()" disabled>
                <i class="fas fa-file-pdf"></i> Testovať PDF generovanie
            </button>
            <button onclick="location.reload()">
                <i class="fas fa-redo"></i> Obnoviť test
            </button>
        </div>

        <div id="resultContainer" style="margin-top: 20px;"></div>
    </div>

    <!-- Load html2pdf.js -->
    <script src="node_modules/html2pdf.js/dist/html2pdf.bundle.min.js"></script>
    <script>
        // Set current date and time
        document.getElementById('testDate').textContent = new Date().toLocaleDateString('sk-SK');
        document.getElementById('testTime').textContent = new Date().toLocaleTimeString('sk-SK');

        // Check html2pdf availability
        window.addEventListener('load', function() {
            setTimeout(checkHtml2PdfStatus, 500);
        });

        function checkHtml2PdfStatus() {
            const statusContainer = document.getElementById('statusContainer');
            const testBtn = document.getElementById('testBtn');
            
            if (typeof window.html2pdf !== 'undefined') {
                statusContainer.innerHTML = `
                    <div class="status success">
                        <i class="fas fa-check-circle"></i> html2pdf.js je úspešne načítaný a pripravený na použitie!
                    </div>
                `;
                testBtn.disabled = false;
                console.log('html2pdf.js je dostupný:', window.html2pdf);
            } else {
                statusContainer.innerHTML = `
                    <div class="status error">
                        <i class="fas fa-times-circle"></i> html2pdf.js nie je dostupný! PDF generovanie nebude fungovať.
                    </div>
                `;
                testBtn.disabled = true;
                console.error('html2pdf.js nie je dostupný');
            }
        }

        async function testPDFGeneration() {
            const testBtn = document.getElementById('testBtn');
            const resultContainer = document.getElementById('resultContainer');
            
            // Show loading state
            testBtn.disabled = true;
            testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Generujem PDF...';
            
            try {
                // Configure html2pdf options
                const options = {
                    margin: [10, 10, 10, 10],
                    filename: `test-html2pdf-${new Date().toISOString().split('T')[0]}.pdf`,
                    image: { type: 'jpeg', quality: 0.98 },
                    html2canvas: {
                        scale: 2,
                        useCORS: true,
                        letterRendering: true
                    },
                    jsPDF: {
                        unit: 'mm',
                        format: 'a4',
                        orientation: 'portrait',
                        compress: true
                    }
                };

                // Generate PDF from test content
                const element = document.getElementById('testContent');
                await html2pdf().set(options).from(element).save();
                
                resultContainer.innerHTML = `
                    <div class="status success">
                        <i class="fas fa-check-circle"></i> PDF bol úspešne vygenerovaný a uložený!
                    </div>
                `;
                
            } catch (error) {
                console.error('Chyba pri generovaní PDF:', error);
                resultContainer.innerHTML = `
                    <div class="status error">
                        <i class="fas fa-times-circle"></i> Chyba pri generovaní PDF: ${error.message}
                    </div>
                `;
            } finally {
                // Reset button
                testBtn.disabled = false;
                testBtn.innerHTML = '<i class="fas fa-file-pdf"></i> Testovať PDF generovanie';
            }
        }
    </script>
</body>
</html>
