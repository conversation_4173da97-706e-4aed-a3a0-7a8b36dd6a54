[debug] [2025-07-03T12:49:05.681Z] ----------------------------------------------------------------------
[debug] [2025-07-03T12:49:05.683Z] Command:       /usr/local/bin/node /Users/<USER>/Documents/augment-projects/Generator/node_modules/.bin/firebase init hosting
[debug] [2025-07-03T12:49:05.683Z] CLI Version:   14.9.0
[debug] [2025-07-03T12:49:05.683Z] Platform:      darwin
[debug] [2025-07-03T12:49:05.684Z] Node Version:  v22.16.0
[debug] [2025-07-03T12:49:05.684Z] Time:          Thu Jul 03 2025 14:49:05 GMT+0200 (Central European Summer Time)
[debug] [2025-07-03T12:49:05.684Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-03T12:49:05.685Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[debug] [2025-07-03T12:49:05.712Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-03T12:49:05.712Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  /Users/<USER>/Documents/augment-projects/Generator

[info] 
=== Project Setup
[info] 
[info] First, let's associate this project directory with a Firebase project.
[info] You can create multiple project aliases by running firebase use --add, 
[info] but for now we'll just set up a default project.
[info] 
[debug] [2025-07-03T12:49:06.059Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-07-03T12:49:06.059Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
[debug] [2025-07-03T12:51:07.735Z] Checked if tokens are valid: false, expires at: 1748808968404
[debug] [2025-07-03T12:51:07.736Z] Checked if tokens are valid: false, expires at: 1748808968404
[debug] [2025-07-03T12:51:07.736Z] > refreshing access token with scopes: []
[debug] [2025-07-03T12:51:07.738Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-07-03T12:51:07.738Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-03T12:51:08.198Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-07-03T12:51:08.198Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-07-03T12:51:08.212Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects pageSize=100
[debug] [2025-07-03T12:51:10.768Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects 200
[debug] [2025-07-03T12:51:10.769Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects [omitted]
