<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDF generovanie</title>
    <script src="node_modules/jspdf/dist/jspdf.umd.min.js"></script>
</head>
<body>
    <h1>Test PDF generovanie</h1>
    <button onclick="testPDF()">Generovať test PDF</button>
    
    <script>
        function testPDF() {
            try {
                console.log('Testing PDF generation...');
                
                if (typeof window.jspdf === 'undefined') {
                    alert('jsPDF nie je dostupné');
                    return;
                }
                
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();
                
                doc.setFont('helvetica');
                doc.setFontSize(20);
                doc.text('Test PDF', 20, 30);
                doc.setFontSize(12);

                // Function to handle Slovak characters
                function fixSlovakText(text) {
                    return text
                        .replace(/č/g, 'c').replace(/Č/g, 'C')
                        .replace(/ť/g, 't').replace(/Ť/g, 'T')
                        .replace(/ň/g, 'n').replace(/Ň/g, 'N')
                        .replace(/ž/g, 'z').replace(/Ž/g, 'Z')
                        .replace(/š/g, 's').replace(/Š/g, 'S')
                        .replace(/ý/g, 'y').replace(/Ý/g, 'Y')
                        .replace(/á/g, 'a').replace(/Á/g, 'A')
                        .replace(/é/g, 'e').replace(/É/g, 'E')
                        .replace(/í/g, 'i').replace(/Í/g, 'I')
                        .replace(/ó/g, 'o').replace(/Ó/g, 'O')
                        .replace(/ú/g, 'u').replace(/Ú/g, 'U')
                        .replace(/ô/g, 'o').replace(/Ô/g, 'O')
                        .replace(/ľ/g, 'l').replace(/Ľ/g, 'L')
                        .replace(/ŕ/g, 'r').replace(/Ŕ/g, 'R')
                        .replace(/ä/g, 'a').replace(/Ä/g, 'A')
                        .replace(/ď/g, 'd').replace(/Ď/g, 'D');
                }

                doc.text(fixSlovakText('Toto je test PDF súbor vytvorený v Electron aplikácii.'), 20, 50);
                doc.text(fixSlovakText('Test diakritiky: čťňžšýáéíóúôľŕäď'), 20, 70);
                doc.text('Kontakt: <EMAIL>', 20, 90);
                doc.text('Tel: +421 951 553 464', 20, 110);
                
                doc.save('test.pdf');
                alert('PDF bolo úspešne vytvorené!');
                
            } catch (error) {
                console.error('Error:', error);
                alert('Chyba: ' + error.message);
            }
        }
    </script>
</body>
</html>
