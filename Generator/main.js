const { app, BrowserWindow, Menu, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// Keep a global reference of the window object
let mainWindow;

// Enable live reload for development
if (process.env.NODE_ENV === 'development') {
  require('electron-reload')(__dirname, {
    electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
    hardResetMethod: 'exit'
  });
}

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 800,
    minHeight: 600,
    movable: true, // Allow window movement
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: false // Allow local file access
    },
    icon: path.join(__dirname, 'assets', 'icon.png'), // Use new icon
    show: false // Don't show until ready
  });

  // Load the index.html file
  const indexPath = path.join(__dirname, 'index.html');
  console.log('Attempting to load index.html from:', indexPath);

  mainWindow.loadFile(indexPath).catch(err => {
    console.error('Failed to load index.html from __dirname:', err);
    // Fallback: try loading from current directory
    const fallbackPath = path.resolve('index.html');
    console.log('Attempting fallback load from:', fallbackPath);
    mainWindow.loadFile(fallbackPath).catch(err2 => {
      console.error('Failed to load index.html from current directory:', err2);
      // Last resort: try relative path
      mainWindow.loadFile('./index.html').catch(err3 => {
        console.error('Failed to load index.html with relative path:', err3);
      });
    });
  });

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Focus on window
    if (process.platform === 'darwin') {
      app.dock.show();
    }
  });

  // Handle load errors
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('Failed to load page:', errorCode, errorDescription);
  });

  // Handle console messages from renderer
  mainWindow.webContents.on('console-message', (event, level, message) => {
    console.log('Renderer console:', level, message);
  });

  // Open DevTools in development
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Create application menu
  createMenu();
}

function createMenu() {
  const template = [
    {
      label: 'Súbor',
      submenu: [
        {
          label: 'Nová ponuka',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.executeJavaScript('location.reload()');
          }
        },
        {
          label: 'Otvoriť ponuku',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: 'JSON súbory', extensions: ['json'] },
                { name: 'Všetky súbory', extensions: ['*'] }
              ]
            });

            if (!result.canceled && result.filePaths.length > 0) {
              try {
                const data = fs.readFileSync(result.filePaths[0], 'utf8');
                mainWindow.webContents.executeJavaScript(`
                  try {
                    const quoteData = ${data};
                    loadQuoteData(quoteData);
                  } catch (error) {
                    alert('Chyba pri načítavaní súboru: ' + error.message);
                  }
                `);
              } catch (error) {
                dialog.showErrorBox('Chyba', 'Nepodarilo sa načítať súbor: ' + error.message);
              }
            }
          }
        },
        {
          label: 'Uložiť ponuku',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow.webContents.executeJavaScript('saveQuote()');
          }
        },
        { type: 'separator' },
        {
          label: 'Generovať PDF',
          accelerator: 'CmdOrCtrl+P',
          click: () => {
            mainWindow.webContents.executeJavaScript('generatePDF()');
          }
        },
        { type: 'separator' },
        {
          label: 'Ukončiť',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Upraviť',
      submenu: [
        { label: 'Späť', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'Dopredu', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'Vystrihnúť', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'Kopírovať', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'Vložiť', accelerator: 'CmdOrCtrl+V', role: 'paste' },
        { label: 'Vybrať všetko', accelerator: 'CmdOrCtrl+A', role: 'selectall' }
      ]
    },
    {
      label: 'Zobrazenie',
      submenu: [
        { label: 'Obnoviť', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'Vynútiť obnovenie', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'Vývojárske nástroje', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'Skutočná veľkosť', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
        { label: 'Priblížiť', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
        { label: 'Oddialiť', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
        { type: 'separator' },
        { label: 'Celá obrazovka', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Okno',
      submenu: [
        { label: 'Minimalizovať', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: 'Zavrieť', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: 'Pomoc',
      submenu: [
        {
          label: 'O aplikácii',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'O aplikácii',
              message: 'Generátor cenových ponúk',
              detail: 'Verzia 1.0.0\n\nAplikácia pre generovanie cenových ponúk pre služby starostlivosti o hrobové miesta.\n\nKontakt: <EMAIL>\nTel: +421 951 553 464\n\n© 2024 eHroby.sk'
            });
          }
        },
        {
          label: 'Návod na použitie',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'Návod na použitie',
              message: 'Ako používať aplikáciu',
              detail: '1. Vyplňte údaje o zákazníkovi\n2. Vyberte požadované služby\n3. Skontrolujte kalkuláciu\n4. Generujte PDF ponuku\n\nPre viac informácií si prečítajte README.md súbor.'
            });
          }
        },
        {
          label: 'Test PDF generovanie',
          click: () => {
            // Create a new window for PDF testing
            const testWindow = new BrowserWindow({
              width: 600,
              height: 400,
              webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                webSecurity: false
              }
            });
            testWindow.loadFile('test-pdf.html');
          }
        }
      ]
    }
  ];

  // macOS specific menu adjustments
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        { label: 'O aplikácii', role: 'about' },
        { type: 'separator' },
        { label: 'Služby', role: 'services', submenu: [] },
        { type: 'separator' },
        { label: 'Skryť aplikáciu', accelerator: 'Command+H', role: 'hide' },
        { label: 'Skryť ostatné', accelerator: 'Command+Shift+H', role: 'hideothers' },
        { label: 'Zobraziť všetko', role: 'unhide' },
        { type: 'separator' },
        { label: 'Ukončiť', accelerator: 'Command+Q', click: () => app.quit() }
      ]
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// App event handlers
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// Handle app certificate errors
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (url.startsWith('https://localhost') || url.startsWith('https://127.0.0.1')) {
    // Ignore certificate errors for localhost
    event.preventDefault();
    callback(true);
  } else {
    callback(false);
  }
});

// IPC handlers for enhanced functionality
ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options);
  return result;
});

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options);
  return result;
});

ipcMain.handle('write-file', async (event, filePath, data) => {
  try {
    fs.writeFileSync(filePath, data);
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Auto-updater (for future use)
if (process.env.NODE_ENV === 'production') {
  // Uncomment when ready to implement auto-updates
  // const { autoUpdater } = require('electron-updater');
  // autoUpdater.checkForUpdatesAndNotify();
}
