<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test diakritiky v PDF</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <style>
        body {
            font-family: 'Inter', Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-content {
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        h1 {
            color: #5e2e60;
            text-align: center;
            margin-bottom: 30px;
        }
        
        h2 {
            color: #5e2e60;
            border-bottom: 2px solid #5e2e60;
            padding-bottom: 10px;
        }
        
        .test-text {
            font-size: 16px;
            line-height: 1.6;
            margin: 15px 0;
        }
        
        .button {
            background: linear-gradient(135deg, #5e2e60 0%, #4a1e4a 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        
        .button:hover {
            transform: translateY(-2px);
        }
        
        .alphabet {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        
        .letter {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test diakritiky v PDF generovaní</h1>
        
        <button class="button" onclick="generateTestPDF()">
            📄 Generovať test PDF s diakritikоu
        </button>
        
        <div class="test-content" id="pdf-test-content">
            <h2>Testovanie slovenských znakov s diakritikоu</h2>
            
            <div class="test-text">
                <strong>Základný text:</strong><br>
                Toto je test slovenského textu s diakritikоu. Všetky znaky by sa mali zobraziť správne v PDF súbore.
            </div>
            
            <div class="test-text">
                <strong>Slovenská abeceda s diakritikоu:</strong>
            </div>
            
            <div class="alphabet">
                <div class="letter">Á á</div>
                <div class="letter">Ä ä</div>
                <div class="letter">Č č</div>
                <div class="letter">Ď ď</div>
                <div class="letter">É é</div>
                <div class="letter">Í í</div>
                <div class="letter">Ĺ ĺ</div>
                <div class="letter">Ľ ľ</div>
                <div class="letter">Ň ň</div>
                <div class="letter">Ó ó</div>
                <div class="letter">Ô ô</div>
                <div class="letter">Ŕ ŕ</div>
                <div class="letter">Š š</div>
                <div class="letter">Ť ť</div>
                <div class="letter">Ú ú</div>
                <div class="letter">Ý ý</div>
                <div class="letter">Ž ž</div>
            </div>
            
            <div class="test-text">
                <strong>Testové vety:</strong><br>
                • Čerstvé jahody sú veľmi chutné a zdravé.<br>
                • Ťažký deň v práci si vyžaduje dobrý odpočinok.<br>
                • Nový učiteľ učí slovenčinu veľmi dobre.<br>
                • Žltá ruža kvitne v našej záhrade.<br>
                • Šťastný človek má veľa priateľov.<br>
                • Ľudia často cestujú do rôznych krajín.<br>
                • Môj brat má rád čítanie kníh.<br>
                • Včera sme videli krásny západ slnka.
            </div>
            
            <div class="test-text">
                <strong>Kontaktné údaje s diakritikоu:</strong><br>
                📧 Email: <EMAIL><br>
                📞 Telefón: +421 951 553 464<br>
                📍 Adresa: Bratislava, Slovenská republika<br>
                🏢 Spoločnosť: Starostlivosť o hrobové miesta
            </div>
            
            <div class="test-text">
                <strong>Dátum a čas:</strong><br>
                Vytvorené: <span id="current-date"></span>
            </div>
        </div>
    </div>

    <script>
        // Set current date
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('sk-SK', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        async function generateTestPDF() {
            try {
                if (typeof window.html2pdf === 'undefined') {
                    alert('html2pdf knižnica nie je dostupná!');
                    return;
                }

                const element = document.getElementById('pdf-test-content');
                
                const options = {
                    margin: [10, 10, 10, 10],
                    filename: `test-diakritika-${new Date().toISOString().split('T')[0]}.pdf`,
                    image: { type: 'jpeg', quality: 0.98 },
                    html2canvas: { 
                        scale: 2,
                        useCORS: true,
                        allowTaint: true,
                        letterRendering: true
                    },
                    jsPDF: { 
                        unit: 'mm', 
                        format: 'a4', 
                        orientation: 'portrait',
                        compress: true
                    }
                };

                await html2pdf().set(options).from(element).save();
                alert('✅ Test PDF s diakritikоu bol úspešne vytvorený!');
                
            } catch (error) {
                console.error('Error:', error);
                alert('❌ Chyba pri generovaní PDF: ' + error.message);
            }
        }
    </script>
</body>
</html>
