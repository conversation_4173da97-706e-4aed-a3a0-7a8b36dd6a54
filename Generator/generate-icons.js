// Simple icon generator for PWA
// This creates placeholder icons until we can generate proper ones

const fs = require('fs');
const path = require('path');

// Create a simple SVG icon as base
const createSVGIcon = (size) => {
  return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" fill="#4a1e4a"/>
  <circle cx="${size/2}" cy="${size/2}" r="${size/3}" fill="#5e2e60"/>
  <text x="${size/2}" y="${size/2 + size/8}" text-anchor="middle" fill="white" font-family="Arial" font-size="${size/4}" font-weight="bold">eH</text>
</svg>`;
};

// Icon sizes needed for PWA
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Create icons directory if it doesn't exist
const iconsDir = path.join(__dirname, 'icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir);
}

// Generate SVG icons for each size
iconSizes.forEach(size => {
  const svgContent = createSVGIcon(size);
  const filename = `icon-${size}x${size}.svg`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent);
  console.log(`Created ${filename}`);
});

// Create shortcut icons
const shortcuts = ['today', 'camera', 'customers'];
shortcuts.forEach(shortcut => {
  const svgContent = createSVGIcon(96);
  const filename = `shortcut-${shortcut}.svg`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent);
  console.log(`Created ${filename}`);
});

console.log('✅ All icons generated successfully!');
console.log('📝 Note: These are placeholder SVG icons. For production, consider using proper PNG icons.');
