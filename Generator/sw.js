// Service Worker for eHroby PWA
// Provides offline functionality and caching

const CACHE_NAME = 'ehroby-v1.0.0';
const STATIC_CACHE_NAME = 'ehroby-static-v1.0.0';
const DYNAMIC_CACHE_NAME = 'ehroby-dynamic-v1.0.0';

// Files to cache for offline use
const STATIC_FILES = [
  '/',
  '/index.html',
  '/styles.css',
  '/script.js',
  '/orders.js',
  '/crm-invoicing.js',
  '/crm-system.js',
  '/cleaning-system.js',
  '/manifest.json',
  'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
  'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
  'https://storage.googleapis.com/espomienka/logo36.png'
];

// Firebase URLs to cache
const FIREBASE_URLS = [
  'https://www.gstatic.com/firebasejs/',
  'https://ehroby-e3388.firebaseapp.com/',
  'https://ehroby-e3388.firebasestorage.app/'
];

// Install event - cache static files
self.addEventListener('install', event => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('Service Worker: Static files cached successfully');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('Service Worker: Error caching static files:', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME &&
                cacheName.startsWith('ehroby-')) {
              console.log('Service Worker: Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated successfully');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Handle Firebase requests
  if (isFirebaseRequest(url)) {
    event.respondWith(handleFirebaseRequest(request));
    return;
  }

  // Handle static files
  if (isStaticFile(url)) {
    event.respondWith(handleStaticRequest(request));
    return;
  }

  // Handle dynamic content
  event.respondWith(handleDynamicRequest(request));
});

// Check if request is for Firebase
function isFirebaseRequest(url) {
  return FIREBASE_URLS.some(firebaseUrl => url.href.includes(firebaseUrl)) ||
         url.hostname.includes('firebase') ||
         url.hostname.includes('firestore') ||
         url.hostname.includes('googleapis');
}

// Check if request is for static file
function isStaticFile(url) {
  return STATIC_FILES.some(file => url.pathname === file || url.href === file) ||
         url.pathname.endsWith('.css') ||
         url.pathname.endsWith('.js') ||
         url.pathname.endsWith('.png') ||
         url.pathname.endsWith('.jpg') ||
         url.pathname.endsWith('.ico');
}

// Handle Firebase requests - network first, cache fallback
async function handleFirebaseRequest(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache successful Firebase responses
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Service Worker: Firebase network failed, trying cache');
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page for Firebase failures
    return new Response(
      JSON.stringify({ 
        error: 'Offline', 
        message: 'Aplikácia pracuje v offline režime' 
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Handle static files - cache first, network fallback
async function handleStaticRequest(request) {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Service Worker: Failed to fetch static file:', error);
    
    // Return offline fallback for main page
    if (request.url.includes('index.html') || request.url === self.location.origin + '/') {
      return caches.match('/index.html');
    }
    
    return new Response('Offline', { status: 503 });
  }
}

// Handle dynamic content - network first, cache fallback
async function handleDynamicRequest(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    return new Response('Offline', { status: 503 });
  }
}

// Background sync for offline data
self.addEventListener('sync', event => {
  console.log('Service Worker: Background sync triggered:', event.tag);
  
  if (event.tag === 'sync-cleaning-data') {
    event.waitUntil(syncCleaningData());
  }
});

// Sync cleaning data when online
async function syncCleaningData() {
  try {
    console.log('Service Worker: Syncing cleaning data...');
    
    // Get offline data from IndexedDB
    const offlineData = await getOfflineData();
    
    if (offlineData && offlineData.length > 0) {
      // Send to Firebase
      const response = await fetch('/api/sync-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(offlineData)
      });
      
      if (response.ok) {
        console.log('Service Worker: Data synced successfully');
        await clearOfflineData();
      }
    }
  } catch (error) {
    console.error('Service Worker: Sync failed:', error);
  }
}

// Get offline data (placeholder - will be implemented with IndexedDB)
async function getOfflineData() {
  // This will be implemented when we add IndexedDB
  return [];
}

// Clear offline data after successful sync
async function clearOfflineData() {
  // This will be implemented when we add IndexedDB
  console.log('Service Worker: Offline data cleared');
}

// Push notification handling
self.addEventListener('push', event => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: event.data ? event.data.text() : 'Nová notifikácia z eHroby',
    icon: 'https://storage.googleapis.com/espomienka/logo36.png',
    badge: 'https://storage.googleapis.com/espomienka/logo36.png',
    vibrate: [200, 100, 200],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'Otvoriť aplikáciu',
        icon: 'icons/checkmark.png'
      },
      {
        action: 'close',
        title: 'Zavrieť',
        icon: 'icons/xmark.png'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('eHroby', options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', event => {
  console.log('Service Worker: Notification clicked');
  
  event.notification.close();
  
  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

console.log('Service Worker: Loaded successfully');
