<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test</title>
</head>
<body>
    <h1>Test aplikácie</h1>
    <p>Ak vidíte tú<PERSON>, aplikácia funguje správne.</p>
    
    <script src="html2pdf.bundle.min.js"></script>
    <script>
        console.log('Test script loaded');
        if (typeof window.html2pdf !== 'undefined') {
            console.log('html2pdf is available!');
            document.body.innerHTML += '<p style="color: green;">✓ html2pdf.js je dostupný!</p>';
        } else {
            console.log('html2pdf is NOT available');
            document.body.innerHTML += '<p style="color: red;">✗ html2pdf.js nie je dostupný</p>';
        }
    </script>
</body>
</html>
