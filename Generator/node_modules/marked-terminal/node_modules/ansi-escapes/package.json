{"name": "ansi-escapes", "version": "7.0.0", "description": "ANSI escape codes for manipulating the terminal", "license": "MIT", "repository": "sindresorhus/ansi-escapes", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "ava && tsd", "//test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "base.js", "base.d.ts"], "keywords": ["ansi", "terminal", "console", "cli", "string", "tty", "escape", "escapes", "formatting", "shell", "xterm", "log", "logging", "command-line", "text", "vt100", "sequence", "control", "code", "codes", "cursor", "iterm", "iterm2", "clear", "screen", "erase", "scrollback"], "dependencies": {"environment": "^1.0.0"}, "devDependencies": {"@types/node": "20.12.8", "ava": "^6.1.2", "tsd": "0.31.0", "xo": "^0.58.0"}}