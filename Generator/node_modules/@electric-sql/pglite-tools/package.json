{"name": "@electric-sql/pglite-tools", "version": "0.2.9", "description": "Tools for working with PGlite databases", "author": "Electric DB Limited", "homepage": "https://pglite.dev", "license": "Apache-2.0", "keywords": ["postgres", "sql", "database", "wasm", "pglite", "pg_dump", "pg_restore"], "private": false, "publishConfig": {"access": "public"}, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./pg_dump": {"import": {"types": "./dist/pg_dump.d.ts", "default": "./dist/pg_dump.js"}, "require": {"types": "./dist/pg_dump.d.cts", "default": "./dist/pg_dump.cjs"}}}, "browser": {"fs": false, "fs/promises": false}, "devDependencies": {"@arethetypeswrong/cli": "^0.18.1", "@types/emscripten": "^1.39.13", "@types/node": "^20.16.11", "tsx": "^4.19.2", "vitest": "^1.3.1", "@electric-sql/pglite": "0.3.4"}, "peerDependencies": {"@electric-sql/pglite": "0.3.4"}, "scripts": {"build": "tsup && tsx ./scripts/bundle-wasm.ts", "check:exports": "attw . --pack --profile node16", "lint": "eslint ./src ./tests --report-unused-disable-directives --max-warnings 0", "format": "prettier --write ./src ./tests", "typecheck": "tsc", "stylecheck": "pnpm lint && prettier --check ./src ./tests", "test": "vitest"}}