# @electric-sql/pglite-tools

## 0.2.9

### Patch Changes

- 38a55d0: fix cjs/esm misconfigurations
- Updated dependencies [1fcaa3e]
- Updated dependencies [38a55d0]
- Updated dependencies [aac7003]
- Updated dependencies [8ca254d]
  - @electric-sql/pglite@0.3.4

## 0.2.8

### Patch Changes

- Updated dependencies [ea2c7c7]
  - @electric-sql/pglite@0.3.3

## 0.2.7

### Patch Changes

- Updated dependencies [e2c654b]
  - @electric-sql/pglite@0.3.2

## 0.2.6

### Patch Changes

- Updated dependencies [713364e]
  - @electric-sql/pglite@0.3.1

## 0.2.5

### Patch Changes

- 317fd36: Specify a peer dependency range on @electric-sql/pglite
- Updated dependencies [97e52f7]
- Updated dependencies [4356024]
- Updated dependencies [0033bc7]
  - @electric-sql/pglite@0.3.0

## 0.2.4

### Patch Changes

- bbfa9f1: Restore SEARCH_PATH after pg_dump

## 0.2.3

### Patch Changes

- 8545760: pg_dump error messages set on the thrown Error
- d26e658: Run a DEALLOCATE ALL after each pg_dump to cleanup the prepared statements.

## 0.2.2

### Patch Changes

- 17c9875: add node imports to the package.json browser excludes

## 0.2.1

### Patch Changes

- 6547374: Alpha version of pg_dump support in the browser and Node using a WASM build of pg_dump
