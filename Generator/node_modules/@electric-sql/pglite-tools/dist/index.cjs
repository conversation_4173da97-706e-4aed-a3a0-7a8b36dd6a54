"use strict";var Z=Object.create;var A=Object.defineProperty;var J=Object.getOwnPropertyDescriptor;var tt=Object.getOwnPropertyNames;var et=Object.getPrototypeOf,st=Object.prototype.hasOwnProperty;var nt=(p,t)=>{for(var e in t)A(p,e,{get:t[e],enumerable:!0})},k=(p,t,e,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of tt(t))!st.call(p,r)&&r!==e&&A(p,r,{get:()=>t[r],enumerable:!(n=J(t,r))||n.enumerable});return p};var rt=(p,t,e)=>(e=p!=null?Z(et(p)):{},k(t||!p||!p.__esModule?A(e,"default",{value:p,enumerable:!0}):e,p)),it=p=>k(A({},"__esModule",{value:!0}),p);var Kt={};nt(Kt,{pgDump:()=>vt});module.exports=it(Kt);var ot=()=>typeof document>"u"?new URL(`file:${__filename}`).href:document.currentScript&&document.currentScript.src||new URL("main.js",document.baseURI).href,y=ot();var T=class extends Error{constructor(t){super(`Exit with code ${t}`),this.code=t}},P=class{appendFileSync(t,e,n={}){throw new Error("appendFileSync not implemented")}fsyncSync(t){throw new Error("fsyncSync not implemented")}linkSync(t,e){throw new Error("linkSync not implemented")}mkdirSync(t,e={}){throw new Error("mkdirSync not implemented")}readdirSync(t,e={}){throw new Error("readdirSync not implemented")}readFileSync(t,e={}){throw new Error("readFileSync not implemented")}readlinkSync(t,e={}){throw new Error("readlinkSync not implemented")}renameSync(t,e){throw new Error("renameSync not implemented")}rmdirSync(t,e={}){throw new Error("rmdirSync not implemented")}setFlagsSync(t,e){throw new Error("setFlagsSync not implemented")}statSync(t,e={}){throw new Error("statSync not implemented")}symlinkSync(t,e,n="file"){throw new Error("symlinkSync not implemented")}truncateSync(t,e=0){throw new Error("truncateSync not implemented")}unlinkSync(t){throw new Error("unlinkSync not implemented")}utimesSync(t,e,n){throw new Error("utimesSync not implemented")}writeFileSync(t,e,n={}){throw new Error("writeFileSync not implemented")}},w=class{constructor(t={}){if(this.args=t.args||[],this.env=t.env||{},this.fs=t.fs||new P,!this.fs)throw new Error("File system implementation required");this.fds=new Map([[0,{type:"stdio"}],[1,{type:"stdio"}],[2,{type:"stdio"}],[3,{type:"directory",preopenPath:"/"}]]),this.nextFd=this.fds.size,this.textDecoder=new TextDecoder,this.textEncoder=new TextEncoder,this.args_get=this.args_get.bind(this),this.args_sizes_get=this.args_sizes_get.bind(this),this.environ_get=this.environ_get.bind(this),this.environ_sizes_get=this.environ_sizes_get.bind(this),this.clock_res_get=this.clock_res_get.bind(this),this.clock_time_get=this.clock_time_get.bind(this),this.fd_close=this.fd_close.bind(this),this.fd_seek=this.fd_seek.bind(this),this.fd_write=this.fd_write.bind(this),this.fd_read=this.fd_read.bind(this),this.fd_fdstat_get=this.fd_fdstat_get.bind(this),this.fd_fdstat_set_flags=this.fd_fdstat_set_flags.bind(this),this.fd_prestat_get=this.fd_prestat_get.bind(this),this.fd_prestat_dir_name=this.fd_prestat_dir_name.bind(this),this.path_open=this.path_open.bind(this),this.path_filestat_get=this.path_filestat_get.bind(this),this.proc_exit=this.proc_exit.bind(this),this.fd_advise=this.fd_advise.bind(this),this.fd_allocate=this.fd_allocate.bind(this),this.fd_datasync=this.fd_datasync.bind(this),this.fd_filestat_get=this.fd_filestat_get.bind(this),this.fd_filestat_set_size=this.fd_filestat_set_size.bind(this),this.fd_filestat_set_times=this.fd_filestat_set_times.bind(this),this.fd_pread=this.fd_pread.bind(this),this.fd_pwrite=this.fd_pwrite.bind(this),this.fd_readdir=this.fd_readdir.bind(this),this.fd_renumber=this.fd_renumber.bind(this),this.fd_sync=this.fd_sync.bind(this),this.fd_tell=this.fd_tell.bind(this),this.path_create_directory=this.path_create_directory.bind(this),this.path_filestat_set_times=this.path_filestat_set_times.bind(this),this.path_link=this.path_link.bind(this),this.path_readlink=this.path_readlink.bind(this),this.path_remove_directory=this.path_remove_directory.bind(this),this.path_rename=this.path_rename.bind(this),this.path_symlink=this.path_symlink.bind(this),this.path_unlink_file=this.path_unlink_file.bind(this),this.poll_oneoff=this.poll_oneoff.bind(this),this.sock_accept=this.sock_accept.bind(this),this.sock_recv=this.sock_recv.bind(this),this.sock_send=this.sock_send.bind(this),this.sock_shutdown=this.sock_shutdown.bind(this),this.random_get=this.random_get.bind(this),this.sched_yield=this.sched_yield.bind(this)}setup(t){this.wasm=t}start(t){this.setup(t);try{return t._start&&t._start(),0}catch(e){if(e instanceof T)return e.code;throw e}}stdin(){return new Uint8Array}stdout(t){let e=this.textDecoder.decode(t).replace(/\n$/g,"");e&&console.log(e)}stderr(t){let e=this.textDecoder.decode(t).replace(/\n$/g,"");e&&console.error(e)}args_get(t,e){let n=new DataView(this.wasm.memory.buffer),r=new Uint8Array(this.wasm.memory.buffer);for(let s of this.args){n.setUint32(t,e,!0),t+=4;let i=this.textEncoder.encode(s);r.set(i,e),r[e+i.length]=0,e+=i.length+1}return 0}args_sizes_get(t,e){let n=new DataView(this.wasm.memory.buffer);n.setUint32(t,this.args.length,!0);let r=this.args.reduce((s,i)=>s+i.length+1,0);return n.setUint32(e,r,!0),0}environ_get(t,e){let n=new DataView(this.wasm.memory.buffer),r=new Uint8Array(this.wasm.memory.buffer);for(let[s,i]of Object.entries(this.env)){n.setUint32(t,e,!0),t+=4;let _=`${s}=${i}\0`,f=this.textEncoder.encode(_);r.set(f,e),e+=f.length}return 0}environ_sizes_get(t,e){let n=new DataView(this.wasm.memory.buffer),r=Object.keys(this.env).length;n.setUint32(t,r,!0);let s=Object.entries(this.env).reduce((i,[_,f])=>i+_.length+f.length+2,0);return n.setUint32(e,s,!0),0}clock_res_get(t,e){let n=new DataView(this.wasm.memory.buffer),r;switch(t){case 0:r=1000000n;break;case 1:r=1000n;break;default:return 28}return n.setBigUint64(e,r,!0),0}clock_time_get(t,e,n){let r=new DataView(this.wasm.memory.buffer),s;switch(t){case 0:{let i=Date.now();s=BigInt(i)*1000000n;break}case 1:{s=BigInt(Math.round(performance.now()*1e6));break}default:return 28}return r.setBigUint64(n,s,!0),0}fd_close(t){return this.fds.get(t)?(this.fds.delete(t),0):8}fd_seek(t,e,n,r){let s=this.fds.get(t);if(!s)return 8;if(s.type==="stdio")return 70;var i=null;let _=0,f=Number(e);try{i=this.fs.statSync(s.handle.path)}catch{return 29}switch(n){case 0:_=f;break;case 1:_=Number(s.handle.position)+f;break;case 2:_=Number(i.size)+f;break;default:return console.error("fd_seek invalid mode",n),28}return s.handle.position=_,new DataView(this.wasm.memory.buffer).setBigUint64(r,BigInt(_),!0),0}fd_write(t,e,n,r){let s=0,i=[],_=new DataView(this.wasm.memory.buffer),f=new Uint8Array(this.wasm.memory.buffer);for(let o=0;o<n;o++){let c=e+o*8,h=_.getUint32(c,!0),E=_.getUint32(c+4,!0);i.push(new Uint8Array(f.buffer,h,E)),s+=E}let a;if(i.length===1)a=i[0];else{a=new Uint8Array(s);let o=0;for(let c of i)a.set(c,o),o+=c.length}if(t===1)this.stdout(a);else if(t===2)this.stderr(a);else{let o=this.fds.get(t);if(!o)return 8;o.handle.position+=s;try{this.fs.writeFileSync(o.handle.path,a)}catch{return 29}}return _.setUint32(r,s,!0),0}fd_read(t,e,n,r){let s=this.fds.get(t);if(!s)return 8;let i=0,_=new DataView(this.wasm.memory.buffer),f=new Uint8Array(this.wasm.memory.buffer);try{let a;t===0?a=this.stdin():a=this.fs.readFileSync(s.handle.path);for(let o=0;o<n;o++){let c=e+o*8,h=_.getUint32(c,!0),E=_.getUint32(c+4,!0),S=s.handle.position,l=Math.min(S+E,a.length),O=l-S;if(O<=0||(f.set(new Uint8Array(a.slice(S,l)),h),i+=O,s.handle.position+=O,O<E))break}return _.setUint32(r,i,!0),0}catch{return 29}}path_open(t,e,n,r,s,i,_,f,a){var o=this.fds.get(t);if(!o)return 8;let h=new Uint8Array(this.wasm.memory.buffer).slice(n,n+r),E=this.textDecoder.decode(h),S=E;var l=0;let O=new DataView(this.wasm.memory.buffer);o.preopenPath&&(E.startsWith("/")&&(S=E.slice(1)),S=o.preopenPath+(o.preopenPath.endsWith("/")?"":"/")+S);var u=!1,m=null;let zt=(s&1)==1,Xt=(s&2)==2,G=(s&4)==4,B=(s&8)==8;try{m=this.fs.statSync(S),u=!0}catch{}if((G||B)&&G&&u)return O.setUint32(a,l,!0),20;let j={path:S,position:0},Q="file";l=this.nextFd++;let q={type:Q,handle:j,fd:l};return this.fds.set(l,q),o=this.fds.get(l),o.handle.position=0,B&&(o.handle.size=0),O.setUint32(a,l,!0),0}proc_exit(t){throw new T(t)}fd_fdstat_get(t,e){let n=this.fds.get(t);if(!n)return 8;let r=new DataView(this.wasm.memory.buffer),s;switch(n.type){case"stdio":s=2;break;case"directory":s=3;break;case"file":s=4;break;default:s=0}r.setUint8(e,s);let i=0;n.append&&(i|=1),r.setUint16(e+2,i,!0);let _=0n;n.type==="file"?_=2097254:n.type==="directory"&&(_=100688384);let f=BigInt(_);return r.setBigUint64(e+8,f,!0),r.setBigUint64(e+16,f,!0),0}fd_fdstat_set_flags(t,e){let n=this.fds.get(t);if(!n)return 8;let r=31;if(e&~r)return 28;if(n.type==="stdio")return 58;try{return n.append=!!(e&1),n.handle&&typeof this.fs.setFlagsSync=="function"&&this.fs.setFlagsSync(n.handle,e),0}catch{return 29}}fd_prestat_get(t,e){let n=this.fds.get(t);if(!n)return 8;if(n.type!=="directory")return 8;if(!n.preopenPath)return 8;let r=new DataView(this.wasm.memory.buffer);r.setUint8(e,0);let s=n.preopenPath.length;return r.setUint32(e+4,s,!0),0}fd_prestat_dir_name(t,e,n){let r=this.fds.get(t);if(!r)return 8;if(r.type!=="directory")return 8;if(!r.preopenPath)return 8;if(n<r.preopenPath.length)return 37;let s=new Uint8Array(this.wasm.memory.buffer),i=this.textEncoder.encode(r.preopenPath);return s.set(i,e),0}path_filestat_get(t,e,n,r,s){let i=this.fds.get(t);if(!i)return 8;let _=new Uint8Array(this.wasm.memory.buffer),f=new Uint8Array(_.buffer,n,r),a=this.textDecoder.decode(f);try{let o=a;i.preopenPath&&(a.startsWith("/")&&(o=a.slice(1)),o=i.preopenPath+(i.preopenPath.endsWith("/")?"":"/")+o);let c=this.fs.statSync(o,{followSymlinks:(e&void 0)!==0}),h=new DataView(this.wasm.memory.buffer);h.setBigUint64(s,BigInt(c.dev||0),!0),h.setBigUint64(s+8,BigInt(c.ino||0),!0);let E=0;return c.isFile()?E=4:c.isDirectory()?E=3:c.isSymbolicLink()?E=7:c.isCharacterDevice()?E=2:c.isBlockDevice()?E=1:c.isFIFO()&&(E=6),h.setUint8(s+16,E),h.setBigUint64(s+24,BigInt(c.nlink||1),!0),h.setBigUint64(s+32,BigInt(c.size||0),!0),h.setBigUint64(s+40,BigInt(c.atimeMs*1e6),!0),h.setBigUint64(s+48,BigInt(c.mtimeMs*1e6),!0),h.setBigUint64(s+56,BigInt(c.ctimeMs*1e6),!0),0}catch(o){return o.code==="ENOENT"?44:o.code==="EACCES"?2:29}}fd_advise(t,e,n,r){let s=this.fds.get(t);return s?s.type!=="file"?8:0:8}fd_allocate(t,e,n){let r=this.fds.get(t);if(!r)return 8;if(r.type!=="file")return 8;try{let s=this.fs.statSync(r.handle.path),i=Number(e)+Number(n);if(i>s.size){let _=new Uint8Array(i-s.size);this.fs.appendFileSync(r.handle.path,_)}return 0}catch{return 29}}fd_datasync(t){let e=this.fds.get(t);if(!e)return 8;if(e.type!=="file")return 8;try{return typeof this.fs.fsyncSync=="function"&&this.fs.fsyncSync(e.handle.path),0}catch{return 29}}fd_filestat_get(t,e){let n=this.fds.get(t);if(!n)return 8;if(!n.handle)return 8;let r=new DataView(this.wasm.memory.buffer),s=this.fs.statSync(n.handle.path);return r.setBigUint64(e,BigInt(s.dev),!0),r.setBigUint64(e+8,BigInt(s.ino),!0),r.setUint8(e+16,s.filetype),r.setBigUint64(e+24,BigInt(s.nlink),!0),r.setBigUint64(e+32,BigInt(s.size),!0),r.setBigUint64(e+38,BigInt(s.atime),!0),r.setBigUint64(e+46,BigInt(s.mtime),!0),r.setBigUint64(e+52,BigInt(s.ctime),!0),0}fd_filestat_set_size(t,e){let n=this.fds.get(t);if(!n)return 8;if(n.type!=="file")return 8;try{return this.fs.truncateSync(n.handle.path,Number(e)),0}catch{return 29}}fd_filestat_set_times(t,e,n,r){let s=this.fds.get(t);if(!s)return 8;if(s.type!=="file")return 8;try{let i={atime:Number(e)/1e9,mtime:Number(n)/1e9};return this.fs.utimesSync(s.handle.path,i.atime,i.mtime),0}catch{return 29}}fd_pread(t,e,n,r,s){let i=this.fds.get(t);if(!i)return 8;if(i.type!=="file")return 8;try{let _=this.fs.readFileSync(i.handle.path),f=0,a=new DataView(this.wasm.memory.buffer),o=new Uint8Array(this.wasm.memory.buffer),c=Number(r);for(let h=0;h<n;h++){let E=e+h*8,S=a.getUint32(E,!0),l=a.getUint32(E+4,!0),O=c+f,u=Math.min(O+l,_.length),m=u-O;if(m<=0||(o.set(new Uint8Array(_.slice(O,u)),S),f+=m,m<l))break}return a.setUint32(s,f,!0),0}catch{return 29}}fd_pwrite(t,e,n,r,s){let i=this.fds.get(t);if(!i)return 8;if(i.type!=="file")return 8;try{let _=0,f=[],a=new DataView(this.wasm.memory.buffer),o=new Uint8Array(this.wasm.memory.buffer);for(let S=0;S<n;S++){let l=e+S*8,O=a.getUint32(l,!0),u=a.getUint32(l+4,!0);f.push(new Uint8Array(o.buffer,O,u)),_+=u}let c;if(f.length===1)c=f[0];else{c=new Uint8Array(_);let S=0;for(let l of f)c.set(l,S),S+=l.length}let h=this.fs.readFileSync(i.handle.path),E=new Uint8Array(Math.max(Number(r)+c.length,h.length));return E.set(h),E.set(c,Number(r)),this.fs.writeFileSync(i.handle.path,E),a.setUint32(s,_,!0),0}catch{return 29}}fd_readdir(t,e,n,r,s){let i=this.fds.get(t);if(!i)return 8;if(i.type!=="directory")return 54;try{let _=this.fs.readdirSync(i.handle.path,{withFileTypes:!0}),f=new DataView(this.wasm.memory.buffer),a=new Uint8Array(this.wasm.memory.buffer),o=0,c=0,h=Number(r);for(let E=h;E<_.length;E++){let S=_[E],l=S.name,O=this.textEncoder.encode(l),u=24+O.length;if(o+u>n)break;f.setBigUint64(e+o,BigInt(E+1),!0),f.setBigUint64(e+o+8,0n,!0),f.setUint32(e+o+16,O.length,!0);let m=0;S.isFile()?m=4:S.isDirectory()&&(m=3),f.setUint8(e+o+20,m),a.set(O,e+o+24),o+=u,c++}return f.setUint32(s,o,!0),0}catch{return 29}}fd_renumber(t,e){let n=this.fds.get(t);return n?(this.fds.delete(e),this.fds.set(e,n),this.fds.delete(t),0):8}fd_sync(t){let e=this.fds.get(t);if(!e)return 8;if(e.type!=="file")return 8;try{return typeof this.fs.fsyncSync=="function"&&this.fs.fsyncSync(e.handle.path),0}catch{return 29}}fd_tell(t,e){let n=this.fds.get(t);return n?n.type!=="file"?8:(new DataView(this.wasm.memory.buffer).setBigUint64(e,BigInt(n.handle.position),!0),0):8}path_create_directory(t,e,n){let r=this.fds.get(t);if(!r)return 8;let s=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,e,n));try{let i=s;return r.preopenPath&&(s.startsWith("/")&&(i=s.slice(1)),i=r.preopenPath+"/"+i),this.fs.mkdirSync(i),0}catch{return 29}}path_filestat_set_times(t,e,n,r,s,i,_){let f=this.fds.get(t);if(!f)return 8;let a=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,n,r));try{let o=a;f.preopenPath&&(a.startsWith("/")&&(o=a.slice(1)),o=f.preopenPath+"/"+o);let c={atime:Number(s)/1e9,mtime:Number(i)/1e9};return this.fs.utimesSync(o,c.atime,c.mtime),0}catch{return 29}}path_link(t,e,n,r,s,i,_){let f=this.fds.get(t),a=this.fds.get(s);if(!f||!a)return 8;let o=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,n,r)),c=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,i,_));try{let h=o,E=c;return f.preopenPath&&(o.startsWith("/")&&(h=o.slice(1)),h=f.preopenPath+"/"+h),a.preopenPath&&(c.startsWith("/")&&(E=c.slice(1)),E=a.preopenPath+"/"+E),this.fs.linkSync(h,E),0}catch{return 29}}path_readlink(t,e,n,r,s,i){let _=this.fds.get(t);if(!_)return 8;let f=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,e,n));try{let a=f;_.preopenPath&&(f.startsWith("/")&&(a=f.slice(1)),a=_.preopenPath+"/"+a);let o=this.fs.readlinkSync(a),c=this.textEncoder.encode(o);return c.length>s?61:(new Uint8Array(this.wasm.memory.buffer).set(c,r),new DataView(this.wasm.memory.buffer).setUint32(i,c.length,!0),0)}catch{return 29}}path_remove_directory(t,e,n){let r=this.fds.get(t);if(!r)return 8;let s=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,e,n));try{let i=s;return r.preopenPath&&(s.startsWith("/")&&(i=s.slice(1)),i=r.preopenPath+"/"+i),this.fs.rmdirSync(i),0}catch{return 29}}path_rename(t,e,n,r,s,i){let _=this.fds.get(t),f=this.fds.get(r);if(!_||!f)return 8;let a=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,e,n)),o=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,s,i));try{let c=a,h=o;return _.preopenPath&&(a.startsWith("/")&&(c=a.slice(1)),c=_.preopenPath+"/"+c),f.preopenPath&&(o.startsWith("/")&&(h=o.slice(1)),h=f.preopenPath+"/"+h),this.fs.renameSync(c,h),0}catch{return 29}}path_symlink(t,e,n,r,s){let i=this.fds.get(n);if(!i)return 8;let _=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,t,e)),f=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,r,s));try{let a=f;return i.preopenPath&&(f.startsWith("/")&&(a=f.slice(1)),a=i.preopenPath+"/"+a),this.fs.symlinkSync(_,a),0}catch{return 29}}path_unlink_file(t,e,n){let r=this.fds.get(t);if(!r)return 8;let s=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,e,n));try{let i=s;return r.preopenPath&&(s.startsWith("/")&&(i=s.slice(1)),i=r.preopenPath+"/"+i),this.fs.unlinkSync(i),0}catch{return 29}}poll_oneoff(t,e,n,r){let s=new DataView(this.wasm.memory.buffer),i=0;for(let _=0;_<n;_++){let f=t+_*48,a=s.getBigUint64(f,!0),o=s.getUint8(f+8),c=e+i*32;s.setBigUint64(c,a,!0),s.setUint8(c+8,o),s.setUint8(c+9,1),s.setUint16(c+10,0,!0),i++}return s.setUint32(r,i,!0),0}random_get(t,e){let n=new Uint8Array(this.wasm.memory.buffer,t,e);return crypto.getRandomValues(n),0}sched_yield(){return os.sched_yield(),0}sock_accept(t,e){return 52}sock_recv(t,e,n){return 52}sock_send(t,e,n){return 52}sock_shutdown(t,e){return 52}};var Bt=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string";function kt(p,t){return{...p,...Object.fromEntries(["appendFileSync","fsyncSync","linkSync","setFlagsSync","mkdirSync","readdirSync","readFileSync","readlinkSync","renameSync","rmdirSync","statSync","symlinkSync","truncateSync","unlinkSync","utimesSync","writeFileSync"].map(n=>{let r=n.slice(0,n.length-4);return r in p?[n,(...s)=>(n==="writeFileSync"&&s[0]==="/tmp/out.sql"&&t.push(s[1]),p[r](...s))]:[n,()=>{throw new Error(`${r} not implemented.`)}]}).filter(n=>n!==void 0))}}async function Ht({pg:p,args:t}){let e=new URL("./pg_dump.wasm",y),n=[],r=kt(p.Module.FS,n),s=new w({fs:r,args:["pg_dump",...t],env:{PWD:"/"}});s.stdout=o=>{};let i=new TextDecoder,_="";s.stderr=o=>{let c=i.decode(o);c&&(_+=c)},s.sched_yield=()=>{let o="/tmp/pglite/base/.s.PGSQL.5432.in",c="/tmp/pglite/base/.s.PGSQL.5432.out";if(r.analyzePath(o).exists){let h=r.readFileSync(o);if(r.unlinkSync(o),h[0]===0){let S=new Uint8Array([...Mt,...Wt,...Yt]);return r.writeFileSync(c,S),0}let E=p.execProtocolRawSync(h);r.writeFileSync(c,E)}return 0},await r.writeFile("/pg_dump","\0",{mode:18});let f;if(Bt){let c=await(await import("fs/promises")).readFile(e.toString().slice(7));f=await WebAssembly.instantiate(c,{wasi_snapshot_preview1:s})}else f=await WebAssembly.instantiateStreaming(fetch(e),{wasi_snapshot_preview1:s});let a;return await p.runExclusive(async()=>{a=s.start(f.instance.exports)}),[a,n,_]}async function vt({pg:p,args:t,fileName:e="dump.sql"}){let r=(await p.query("SHOW SEARCH_PATH;")).rows[0].search_path,s="/tmp/out.sql",i=["-U","postgres","--inserts","-j","1","-f",s,"postgres"],[_,f,a]=await Ht({pg:p,args:[...t??[],...i]});if(p.exec(`DEALLOCATE ALL; SET SEARCH_PATH = ${r}`),_!==0)throw new Error(`pg_dump failed with exit code ${_}. 
Error message: ${a}`);let o=new File(f,e,{type:"text/plain"});return p.Module.FS.unlink(s),o}function x(p){return p.charCodeAt(0)}function F(p){let t=new ArrayBuffer(4);return new DataView(t).setInt32(0,p,!1),new Uint8Array(t)}function z(p){let e=new TextEncoder().encode(p);return new Uint8Array([...e,0])}var Mt=new Uint8Array([x("R"),...F(8),...F(0)]),Yt=new Uint8Array([x("Z"),...F(5),x("I")]),X=z("server_version"),$=z("16.3 (PGlite 0.2.0)"),Vt=4+X.length+$.length,Wt=new Uint8Array([x("S"),...F(Vt),...X,...$]);0&&(module.exports={pgDump});
//# sourceMappingURL=index.cjs.map