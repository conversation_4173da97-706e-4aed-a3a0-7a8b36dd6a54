var I=class extends Error{constructor(e){super(`Exit with code ${e}`),this.code=e}},U=class{appendFileSync(e,t,n={}){throw new Error("appendFileSync not implemented")}fsyncSync(e){throw new Error("fsyncSync not implemented")}linkSync(e,t){throw new Error("linkSync not implemented")}mkdirSync(e,t={}){throw new Error("mkdirSync not implemented")}readdirSync(e,t={}){throw new Error("readdirSync not implemented")}readFileSync(e,t={}){throw new Error("readFileSync not implemented")}readlinkSync(e,t={}){throw new Error("readlinkSync not implemented")}renameSync(e,t){throw new Error("renameSync not implemented")}rmdirSync(e,t={}){throw new Error("rmdirSync not implemented")}setFlagsSync(e,t){throw new Error("setFlagsSync not implemented")}statSync(e,t={}){throw new Error("statSync not implemented")}symlinkSync(e,t,n="file"){throw new Error("symlinkSync not implemented")}truncateSync(e,t=0){throw new Error("truncateSync not implemented")}unlinkSync(e){throw new Error("unlinkSync not implemented")}utimesSync(e,t,n){throw new Error("utimesSync not implemented")}writeFileSync(e,t,n={}){throw new Error("writeFileSync not implemented")}},D=class{constructor(e={}){if(this.args=e.args||[],this.env=e.env||{},this.fs=e.fs||new U,!this.fs)throw new Error("File system implementation required");this.fds=new Map([[0,{type:"stdio"}],[1,{type:"stdio"}],[2,{type:"stdio"}],[3,{type:"directory",preopenPath:"/"}]]),this.nextFd=this.fds.size,this.textDecoder=new TextDecoder,this.textEncoder=new TextEncoder,this.args_get=this.args_get.bind(this),this.args_sizes_get=this.args_sizes_get.bind(this),this.environ_get=this.environ_get.bind(this),this.environ_sizes_get=this.environ_sizes_get.bind(this),this.clock_res_get=this.clock_res_get.bind(this),this.clock_time_get=this.clock_time_get.bind(this),this.fd_close=this.fd_close.bind(this),this.fd_seek=this.fd_seek.bind(this),this.fd_write=this.fd_write.bind(this),this.fd_read=this.fd_read.bind(this),this.fd_fdstat_get=this.fd_fdstat_get.bind(this),this.fd_fdstat_set_flags=this.fd_fdstat_set_flags.bind(this),this.fd_prestat_get=this.fd_prestat_get.bind(this),this.fd_prestat_dir_name=this.fd_prestat_dir_name.bind(this),this.path_open=this.path_open.bind(this),this.path_filestat_get=this.path_filestat_get.bind(this),this.proc_exit=this.proc_exit.bind(this),this.fd_advise=this.fd_advise.bind(this),this.fd_allocate=this.fd_allocate.bind(this),this.fd_datasync=this.fd_datasync.bind(this),this.fd_filestat_get=this.fd_filestat_get.bind(this),this.fd_filestat_set_size=this.fd_filestat_set_size.bind(this),this.fd_filestat_set_times=this.fd_filestat_set_times.bind(this),this.fd_pread=this.fd_pread.bind(this),this.fd_pwrite=this.fd_pwrite.bind(this),this.fd_readdir=this.fd_readdir.bind(this),this.fd_renumber=this.fd_renumber.bind(this),this.fd_sync=this.fd_sync.bind(this),this.fd_tell=this.fd_tell.bind(this),this.path_create_directory=this.path_create_directory.bind(this),this.path_filestat_set_times=this.path_filestat_set_times.bind(this),this.path_link=this.path_link.bind(this),this.path_readlink=this.path_readlink.bind(this),this.path_remove_directory=this.path_remove_directory.bind(this),this.path_rename=this.path_rename.bind(this),this.path_symlink=this.path_symlink.bind(this),this.path_unlink_file=this.path_unlink_file.bind(this),this.poll_oneoff=this.poll_oneoff.bind(this),this.sock_accept=this.sock_accept.bind(this),this.sock_recv=this.sock_recv.bind(this),this.sock_send=this.sock_send.bind(this),this.sock_shutdown=this.sock_shutdown.bind(this),this.random_get=this.random_get.bind(this),this.sched_yield=this.sched_yield.bind(this)}setup(e){this.wasm=e}start(e){this.setup(e);try{return e._start&&e._start(),0}catch(t){if(t instanceof I)return t.code;throw t}}stdin(){return new Uint8Array}stdout(e){let t=this.textDecoder.decode(e).replace(/\n$/g,"");t&&console.log(t)}stderr(e){let t=this.textDecoder.decode(e).replace(/\n$/g,"");t&&console.error(t)}args_get(e,t){let n=new DataView(this.wasm.memory.buffer),r=new Uint8Array(this.wasm.memory.buffer);for(let s of this.args){n.setUint32(e,t,!0),e+=4;let i=this.textEncoder.encode(s);r.set(i,t),r[t+i.length]=0,t+=i.length+1}return 0}args_sizes_get(e,t){let n=new DataView(this.wasm.memory.buffer);n.setUint32(e,this.args.length,!0);let r=this.args.reduce((s,i)=>s+i.length+1,0);return n.setUint32(t,r,!0),0}environ_get(e,t){let n=new DataView(this.wasm.memory.buffer),r=new Uint8Array(this.wasm.memory.buffer);for(let[s,i]of Object.entries(this.env)){n.setUint32(e,t,!0),e+=4;let _=`${s}=${i}\0`,f=this.textEncoder.encode(_);r.set(f,t),t+=f.length}return 0}environ_sizes_get(e,t){let n=new DataView(this.wasm.memory.buffer),r=Object.keys(this.env).length;n.setUint32(e,r,!0);let s=Object.entries(this.env).reduce((i,[_,f])=>i+_.length+f.length+2,0);return n.setUint32(t,s,!0),0}clock_res_get(e,t){let n=new DataView(this.wasm.memory.buffer),r;switch(e){case 0:r=1000000n;break;case 1:r=1000n;break;default:return 28}return n.setBigUint64(t,r,!0),0}clock_time_get(e,t,n){let r=new DataView(this.wasm.memory.buffer),s;switch(e){case 0:{let i=Date.now();s=BigInt(i)*1000000n;break}case 1:{s=BigInt(Math.round(performance.now()*1e6));break}default:return 28}return r.setBigUint64(n,s,!0),0}fd_close(e){return this.fds.get(e)?(this.fds.delete(e),0):8}fd_seek(e,t,n,r){let s=this.fds.get(e);if(!s)return 8;if(s.type==="stdio")return 70;var i=null;let _=0,f=Number(t);try{i=this.fs.statSync(s.handle.path)}catch{return 29}switch(n){case 0:_=f;break;case 1:_=Number(s.handle.position)+f;break;case 2:_=Number(i.size)+f;break;default:return console.error("fd_seek invalid mode",n),28}return s.handle.position=_,new DataView(this.wasm.memory.buffer).setBigUint64(r,BigInt(_),!0),0}fd_write(e,t,n,r){let s=0,i=[],_=new DataView(this.wasm.memory.buffer),f=new Uint8Array(this.wasm.memory.buffer);for(let o=0;o<n;o++){let c=t+o*8,h=_.getUint32(c,!0),E=_.getUint32(c+4,!0);i.push(new Uint8Array(f.buffer,h,E)),s+=E}let a;if(i.length===1)a=i[0];else{a=new Uint8Array(s);let o=0;for(let c of i)a.set(c,o),o+=c.length}if(e===1)this.stdout(a);else if(e===2)this.stderr(a);else{let o=this.fds.get(e);if(!o)return 8;o.handle.position+=s;try{this.fs.writeFileSync(o.handle.path,a)}catch{return 29}}return _.setUint32(r,s,!0),0}fd_read(e,t,n,r){let s=this.fds.get(e);if(!s)return 8;let i=0,_=new DataView(this.wasm.memory.buffer),f=new Uint8Array(this.wasm.memory.buffer);try{let a;e===0?a=this.stdin():a=this.fs.readFileSync(s.handle.path);for(let o=0;o<n;o++){let c=t+o*8,h=_.getUint32(c,!0),E=_.getUint32(c+4,!0),p=s.handle.position,l=Math.min(p+E,a.length),O=l-p;if(O<=0||(f.set(new Uint8Array(a.slice(p,l)),h),i+=O,s.handle.position+=O,O<E))break}return _.setUint32(r,i,!0),0}catch{return 29}}path_open(e,t,n,r,s,i,_,f,a){var o=this.fds.get(e);if(!o)return 8;let h=new Uint8Array(this.wasm.memory.buffer).slice(n,n+r),E=this.textDecoder.decode(h),p=E;var l=0;let O=new DataView(this.wasm.memory.buffer);o.preopenPath&&(E.startsWith("/")&&(p=E.slice(1)),p=o.preopenPath+(o.preopenPath.endsWith("/")?"":"/")+p);var u=!1,m=null;let bt=(s&1)==1,Pt=(s&2)==2,b=(s&4)==4,P=(s&8)==8;try{m=this.fs.statSync(p),u=!0}catch{}if((b||P)&&b&&u)return O.setUint32(a,l,!0),20;let $={path:p,position:0},Q="file";l=this.nextFd++;let j={type:Q,handle:$,fd:l};return this.fds.set(l,j),o=this.fds.get(l),o.handle.position=0,P&&(o.handle.size=0),O.setUint32(a,l,!0),0}proc_exit(e){throw new I(e)}fd_fdstat_get(e,t){let n=this.fds.get(e);if(!n)return 8;let r=new DataView(this.wasm.memory.buffer),s;switch(n.type){case"stdio":s=2;break;case"directory":s=3;break;case"file":s=4;break;default:s=0}r.setUint8(t,s);let i=0;n.append&&(i|=1),r.setUint16(t+2,i,!0);let _=0n;n.type==="file"?_=2097254:n.type==="directory"&&(_=100688384);let f=BigInt(_);return r.setBigUint64(t+8,f,!0),r.setBigUint64(t+16,f,!0),0}fd_fdstat_set_flags(e,t){let n=this.fds.get(e);if(!n)return 8;let r=31;if(t&~r)return 28;if(n.type==="stdio")return 58;try{return n.append=!!(t&1),n.handle&&typeof this.fs.setFlagsSync=="function"&&this.fs.setFlagsSync(n.handle,t),0}catch{return 29}}fd_prestat_get(e,t){let n=this.fds.get(e);if(!n)return 8;if(n.type!=="directory")return 8;if(!n.preopenPath)return 8;let r=new DataView(this.wasm.memory.buffer);r.setUint8(t,0);let s=n.preopenPath.length;return r.setUint32(t+4,s,!0),0}fd_prestat_dir_name(e,t,n){let r=this.fds.get(e);if(!r)return 8;if(r.type!=="directory")return 8;if(!r.preopenPath)return 8;if(n<r.preopenPath.length)return 37;let s=new Uint8Array(this.wasm.memory.buffer),i=this.textEncoder.encode(r.preopenPath);return s.set(i,t),0}path_filestat_get(e,t,n,r,s){let i=this.fds.get(e);if(!i)return 8;let _=new Uint8Array(this.wasm.memory.buffer),f=new Uint8Array(_.buffer,n,r),a=this.textDecoder.decode(f);try{let o=a;i.preopenPath&&(a.startsWith("/")&&(o=a.slice(1)),o=i.preopenPath+(i.preopenPath.endsWith("/")?"":"/")+o);let c=this.fs.statSync(o,{followSymlinks:(t&void 0)!==0}),h=new DataView(this.wasm.memory.buffer);h.setBigUint64(s,BigInt(c.dev||0),!0),h.setBigUint64(s+8,BigInt(c.ino||0),!0);let E=0;return c.isFile()?E=4:c.isDirectory()?E=3:c.isSymbolicLink()?E=7:c.isCharacterDevice()?E=2:c.isBlockDevice()?E=1:c.isFIFO()&&(E=6),h.setUint8(s+16,E),h.setBigUint64(s+24,BigInt(c.nlink||1),!0),h.setBigUint64(s+32,BigInt(c.size||0),!0),h.setBigUint64(s+40,BigInt(c.atimeMs*1e6),!0),h.setBigUint64(s+48,BigInt(c.mtimeMs*1e6),!0),h.setBigUint64(s+56,BigInt(c.ctimeMs*1e6),!0),0}catch(o){return o.code==="ENOENT"?44:o.code==="EACCES"?2:29}}fd_advise(e,t,n,r){let s=this.fds.get(e);return s?s.type!=="file"?8:0:8}fd_allocate(e,t,n){let r=this.fds.get(e);if(!r)return 8;if(r.type!=="file")return 8;try{let s=this.fs.statSync(r.handle.path),i=Number(t)+Number(n);if(i>s.size){let _=new Uint8Array(i-s.size);this.fs.appendFileSync(r.handle.path,_)}return 0}catch{return 29}}fd_datasync(e){let t=this.fds.get(e);if(!t)return 8;if(t.type!=="file")return 8;try{return typeof this.fs.fsyncSync=="function"&&this.fs.fsyncSync(t.handle.path),0}catch{return 29}}fd_filestat_get(e,t){let n=this.fds.get(e);if(!n)return 8;if(!n.handle)return 8;let r=new DataView(this.wasm.memory.buffer),s=this.fs.statSync(n.handle.path);return r.setBigUint64(t,BigInt(s.dev),!0),r.setBigUint64(t+8,BigInt(s.ino),!0),r.setUint8(t+16,s.filetype),r.setBigUint64(t+24,BigInt(s.nlink),!0),r.setBigUint64(t+32,BigInt(s.size),!0),r.setBigUint64(t+38,BigInt(s.atime),!0),r.setBigUint64(t+46,BigInt(s.mtime),!0),r.setBigUint64(t+52,BigInt(s.ctime),!0),0}fd_filestat_set_size(e,t){let n=this.fds.get(e);if(!n)return 8;if(n.type!=="file")return 8;try{return this.fs.truncateSync(n.handle.path,Number(t)),0}catch{return 29}}fd_filestat_set_times(e,t,n,r){let s=this.fds.get(e);if(!s)return 8;if(s.type!=="file")return 8;try{let i={atime:Number(t)/1e9,mtime:Number(n)/1e9};return this.fs.utimesSync(s.handle.path,i.atime,i.mtime),0}catch{return 29}}fd_pread(e,t,n,r,s){let i=this.fds.get(e);if(!i)return 8;if(i.type!=="file")return 8;try{let _=this.fs.readFileSync(i.handle.path),f=0,a=new DataView(this.wasm.memory.buffer),o=new Uint8Array(this.wasm.memory.buffer),c=Number(r);for(let h=0;h<n;h++){let E=t+h*8,p=a.getUint32(E,!0),l=a.getUint32(E+4,!0),O=c+f,u=Math.min(O+l,_.length),m=u-O;if(m<=0||(o.set(new Uint8Array(_.slice(O,u)),p),f+=m,m<l))break}return a.setUint32(s,f,!0),0}catch{return 29}}fd_pwrite(e,t,n,r,s){let i=this.fds.get(e);if(!i)return 8;if(i.type!=="file")return 8;try{let _=0,f=[],a=new DataView(this.wasm.memory.buffer),o=new Uint8Array(this.wasm.memory.buffer);for(let p=0;p<n;p++){let l=t+p*8,O=a.getUint32(l,!0),u=a.getUint32(l+4,!0);f.push(new Uint8Array(o.buffer,O,u)),_+=u}let c;if(f.length===1)c=f[0];else{c=new Uint8Array(_);let p=0;for(let l of f)c.set(l,p),p+=l.length}let h=this.fs.readFileSync(i.handle.path),E=new Uint8Array(Math.max(Number(r)+c.length,h.length));return E.set(h),E.set(c,Number(r)),this.fs.writeFileSync(i.handle.path,E),a.setUint32(s,_,!0),0}catch{return 29}}fd_readdir(e,t,n,r,s){let i=this.fds.get(e);if(!i)return 8;if(i.type!=="directory")return 54;try{let _=this.fs.readdirSync(i.handle.path,{withFileTypes:!0}),f=new DataView(this.wasm.memory.buffer),a=new Uint8Array(this.wasm.memory.buffer),o=0,c=0,h=Number(r);for(let E=h;E<_.length;E++){let p=_[E],l=p.name,O=this.textEncoder.encode(l),u=24+O.length;if(o+u>n)break;f.setBigUint64(t+o,BigInt(E+1),!0),f.setBigUint64(t+o+8,0n,!0),f.setUint32(t+o+16,O.length,!0);let m=0;p.isFile()?m=4:p.isDirectory()&&(m=3),f.setUint8(t+o+20,m),a.set(O,t+o+24),o+=u,c++}return f.setUint32(s,o,!0),0}catch{return 29}}fd_renumber(e,t){let n=this.fds.get(e);return n?(this.fds.delete(t),this.fds.set(t,n),this.fds.delete(e),0):8}fd_sync(e){let t=this.fds.get(e);if(!t)return 8;if(t.type!=="file")return 8;try{return typeof this.fs.fsyncSync=="function"&&this.fs.fsyncSync(t.handle.path),0}catch{return 29}}fd_tell(e,t){let n=this.fds.get(e);return n?n.type!=="file"?8:(new DataView(this.wasm.memory.buffer).setBigUint64(t,BigInt(n.handle.position),!0),0):8}path_create_directory(e,t,n){let r=this.fds.get(e);if(!r)return 8;let s=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,t,n));try{let i=s;return r.preopenPath&&(s.startsWith("/")&&(i=s.slice(1)),i=r.preopenPath+"/"+i),this.fs.mkdirSync(i),0}catch{return 29}}path_filestat_set_times(e,t,n,r,s,i,_){let f=this.fds.get(e);if(!f)return 8;let a=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,n,r));try{let o=a;f.preopenPath&&(a.startsWith("/")&&(o=a.slice(1)),o=f.preopenPath+"/"+o);let c={atime:Number(s)/1e9,mtime:Number(i)/1e9};return this.fs.utimesSync(o,c.atime,c.mtime),0}catch{return 29}}path_link(e,t,n,r,s,i,_){let f=this.fds.get(e),a=this.fds.get(s);if(!f||!a)return 8;let o=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,n,r)),c=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,i,_));try{let h=o,E=c;return f.preopenPath&&(o.startsWith("/")&&(h=o.slice(1)),h=f.preopenPath+"/"+h),a.preopenPath&&(c.startsWith("/")&&(E=c.slice(1)),E=a.preopenPath+"/"+E),this.fs.linkSync(h,E),0}catch{return 29}}path_readlink(e,t,n,r,s,i){let _=this.fds.get(e);if(!_)return 8;let f=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,t,n));try{let a=f;_.preopenPath&&(f.startsWith("/")&&(a=f.slice(1)),a=_.preopenPath+"/"+a);let o=this.fs.readlinkSync(a),c=this.textEncoder.encode(o);return c.length>s?61:(new Uint8Array(this.wasm.memory.buffer).set(c,r),new DataView(this.wasm.memory.buffer).setUint32(i,c.length,!0),0)}catch{return 29}}path_remove_directory(e,t,n){let r=this.fds.get(e);if(!r)return 8;let s=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,t,n));try{let i=s;return r.preopenPath&&(s.startsWith("/")&&(i=s.slice(1)),i=r.preopenPath+"/"+i),this.fs.rmdirSync(i),0}catch{return 29}}path_rename(e,t,n,r,s,i){let _=this.fds.get(e),f=this.fds.get(r);if(!_||!f)return 8;let a=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,t,n)),o=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,s,i));try{let c=a,h=o;return _.preopenPath&&(a.startsWith("/")&&(c=a.slice(1)),c=_.preopenPath+"/"+c),f.preopenPath&&(o.startsWith("/")&&(h=o.slice(1)),h=f.preopenPath+"/"+h),this.fs.renameSync(c,h),0}catch{return 29}}path_symlink(e,t,n,r,s){let i=this.fds.get(n);if(!i)return 8;let _=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,e,t)),f=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,r,s));try{let a=f;return i.preopenPath&&(f.startsWith("/")&&(a=f.slice(1)),a=i.preopenPath+"/"+a),this.fs.symlinkSync(_,a),0}catch{return 29}}path_unlink_file(e,t,n){let r=this.fds.get(e);if(!r)return 8;let s=this.textDecoder.decode(new Uint8Array(this.wasm.memory.buffer,t,n));try{let i=s;return r.preopenPath&&(s.startsWith("/")&&(i=s.slice(1)),i=r.preopenPath+"/"+i),this.fs.unlinkSync(i),0}catch{return 29}}poll_oneoff(e,t,n,r){let s=new DataView(this.wasm.memory.buffer),i=0;for(let _=0;_<n;_++){let f=e+_*48,a=s.getBigUint64(f,!0),o=s.getUint8(f+8),c=t+i*32;s.setBigUint64(c,a,!0),s.setUint8(c+8,o),s.setUint8(c+9,1),s.setUint16(c+10,0,!0),i++}return s.setUint32(r,i,!0),0}random_get(e,t){let n=new Uint8Array(this.wasm.memory.buffer,e,t);return crypto.getRandomValues(n),0}sched_yield(){return os.sched_yield(),0}sock_accept(e,t){return 52}sock_recv(e,t,n){return 52}sock_send(e,t,n){return 52}sock_shutdown(e,t){return 52}};var wt=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string";function xt(S,e){return{...S,...Object.fromEntries(["appendFileSync","fsyncSync","linkSync","setFlagsSync","mkdirSync","readdirSync","readFileSync","readlinkSync","renameSync","rmdirSync","statSync","symlinkSync","truncateSync","unlinkSync","utimesSync","writeFileSync"].map(n=>{let r=n.slice(0,n.length-4);return r in S?[n,(...s)=>(n==="writeFileSync"&&s[0]==="/tmp/out.sql"&&e.push(s[1]),S[r](...s))]:[n,()=>{throw new Error(`${r} not implemented.`)}]}).filter(n=>n!==void 0))}}async function Ft({pg:S,args:e}){let t=new URL("./pg_dump.wasm",import.meta.url),n=[],r=xt(S.Module.FS,n),s=new D({fs:r,args:["pg_dump",...e],env:{PWD:"/"}});s.stdout=o=>{};let i=new TextDecoder,_="";s.stderr=o=>{let c=i.decode(o);c&&(_+=c)},s.sched_yield=()=>{let o="/tmp/pglite/base/.s.PGSQL.5432.in",c="/tmp/pglite/base/.s.PGSQL.5432.out";if(r.analyzePath(o).exists){let h=r.readFileSync(o);if(r.unlinkSync(o),h[0]===0){let p=new Uint8Array([...gt,...Ut,...Ct]);return r.writeFileSync(c,p),0}let E=S.execProtocolRawSync(h);r.writeFileSync(c,E)}return 0},await r.writeFile("/pg_dump","\0",{mode:18});let f;if(wt){let c=await(await import("fs/promises")).readFile(t.toString().slice(7));f=await WebAssembly.instantiate(c,{wasi_snapshot_preview1:s})}else f=await WebAssembly.instantiateStreaming(fetch(t),{wasi_snapshot_preview1:s});let a;return await S.runExclusive(async()=>{a=s.start(f.instance.exports)}),[a,n,_]}async function Mt({pg:S,args:e,fileName:t="dump.sql"}){let r=(await S.query("SHOW SEARCH_PATH;")).rows[0].search_path,s="/tmp/out.sql",i=["-U","postgres","--inserts","-j","1","-f",s,"postgres"],[_,f,a]=await Ft({pg:S,args:[...e??[],...i]});if(S.exec(`DEALLOCATE ALL; SET SEARCH_PATH = ${r}`),_!==0)throw new Error(`pg_dump failed with exit code ${_}. 
Error message: ${a}`);let o=new File(f,t,{type:"text/plain"});return S.Module.FS.unlink(s),o}function T(S){return S.charCodeAt(0)}function w(S){let e=new ArrayBuffer(4);return new DataView(e).setInt32(0,S,!1),new Uint8Array(e)}function K(S){let t=new TextEncoder().encode(S);return new Uint8Array([...t,0])}var gt=new Uint8Array([T("R"),...w(8),...w(0)]),Ct=new Uint8Array([T("Z"),...w(5),T("I")]),z=K("server_version"),X=K("16.3 (PGlite 0.2.0)"),Lt=4+z.length+X.length,Ut=new Uint8Array([T("S"),...w(Lt),...z,...X]);export{Mt as a};
//# sourceMappingURL=chunk-QQKFKEQ7.js.map