{"version": 3, "sources": ["../src/wasi/easywasi.js", "../src/pg_dump.ts"], "sourcesContent": ["/* eslint-disable */\n\n// 2024-11-21\n\nimport * as defs from './defs.js'\n\nexport { defs }\n\nexport class WASIProcExit extends Error {\n  constructor(code) {\n    super(`Exit with code ${code}`)\n    this.code = code\n  }\n}\n\n// FS interface that is used here. Implement your own, if you want, or use zenfs or node fs!\nexport class FSDummy {\n  appendFileSync(path, data, options = {}) {\n    throw new Error('appendFileSync not implemented')\n  }\n\n  fsyncSync(fd) {\n    throw new Error('fsyncSync not implemented')\n  }\n\n  linkSync(existingPath, newPath) {\n    throw new Error('linkSync not implemented')\n  }\n\n  mkdirSync(path, options = {}) {\n    throw new Error('mkdirSync not implemented')\n  }\n\n  readdirSync(path, options = {}) {\n    throw new Error('readdirSync not implemented')\n  }\n\n  readFileSync(path, options = {}) {\n    throw new Error('readFileSync not implemented')\n  }\n\n  readlinkSync(path, options = {}) {\n    throw new Error('readlinkSync not implemented')\n  }\n\n  renameSync(oldPath, newPath) {\n    throw new Error('renameSync not implemented')\n  }\n\n  rmdirSync(path, options = {}) {\n    throw new Error('rmdirSync not implemented')\n  }\n\n  setFlagsSync(path, flags) {\n    throw new Error('setFlagsSync not implemented')\n  }\n\n  statSync(path, options = {}) {\n    throw new Error('statSync not implemented')\n  }\n\n  symlinkSync(target, path, type = 'file') {\n    throw new Error('symlinkSync not implemented')\n  }\n\n  truncateSync(path, len = 0) {\n    throw new Error('truncateSync not implemented')\n  }\n\n  unlinkSync(path) {\n    throw new Error('unlinkSync not implemented')\n  }\n\n  utimesSync(path, atime, mtime) {\n    throw new Error('utimesSync not implemented')\n  }\n\n  writeFileSync(path, data, options = {}) {\n    throw new Error('writeFileSync not implemented')\n  }\n}\n\nexport class WasiPreview1 {\n  constructor(options = {}) {\n    this.args = options.args || []\n    this.env = options.env || {}\n    this.fs = options.fs || new FSDummy()\n\n    if (!this.fs) {\n      throw new Error('File system implementation required')\n    }\n\n    // Initialize file descriptors with stdin(0), stdout(1), stderr(2), /\n    // fd is first number\n    this.fds = new Map([\n      [0, { type: 'stdio' }], // stdin\n      [1, { type: 'stdio' }], // stdout\n      [2, { type: 'stdio' }], // stderr\n      [3, { type: 'directory', preopenPath: '/' }], // root directory\n    ])\n\n    this.nextFd = this.fds.size\n    this.textDecoder = new TextDecoder()\n    this.textEncoder = new TextEncoder()\n\n    // Bind all WASI functions to maintain correct 'this' context\n    this.args_get = this.args_get.bind(this)\n    this.args_sizes_get = this.args_sizes_get.bind(this)\n    this.environ_get = this.environ_get.bind(this)\n    this.environ_sizes_get = this.environ_sizes_get.bind(this)\n    this.clock_res_get = this.clock_res_get.bind(this)\n    this.clock_time_get = this.clock_time_get.bind(this)\n    this.fd_close = this.fd_close.bind(this)\n    this.fd_seek = this.fd_seek.bind(this)\n    this.fd_write = this.fd_write.bind(this)\n    this.fd_read = this.fd_read.bind(this)\n    this.fd_fdstat_get = this.fd_fdstat_get.bind(this)\n    this.fd_fdstat_set_flags = this.fd_fdstat_set_flags.bind(this)\n    this.fd_prestat_get = this.fd_prestat_get.bind(this)\n    this.fd_prestat_dir_name = this.fd_prestat_dir_name.bind(this)\n    this.path_open = this.path_open.bind(this)\n    this.path_filestat_get = this.path_filestat_get.bind(this)\n    this.proc_exit = this.proc_exit.bind(this)\n    this.fd_advise = this.fd_advise.bind(this)\n    this.fd_allocate = this.fd_allocate.bind(this)\n    this.fd_datasync = this.fd_datasync.bind(this)\n    this.fd_filestat_get = this.fd_filestat_get.bind(this)\n    this.fd_filestat_set_size = this.fd_filestat_set_size.bind(this)\n    this.fd_filestat_set_times = this.fd_filestat_set_times.bind(this)\n    this.fd_pread = this.fd_pread.bind(this)\n    this.fd_pwrite = this.fd_pwrite.bind(this)\n    this.fd_readdir = this.fd_readdir.bind(this)\n    this.fd_renumber = this.fd_renumber.bind(this)\n    this.fd_sync = this.fd_sync.bind(this)\n    this.fd_tell = this.fd_tell.bind(this)\n    this.path_create_directory = this.path_create_directory.bind(this)\n    this.path_filestat_set_times = this.path_filestat_set_times.bind(this)\n    this.path_link = this.path_link.bind(this)\n    this.path_readlink = this.path_readlink.bind(this)\n    this.path_remove_directory = this.path_remove_directory.bind(this)\n    this.path_rename = this.path_rename.bind(this)\n    this.path_symlink = this.path_symlink.bind(this)\n    this.path_unlink_file = this.path_unlink_file.bind(this)\n    this.poll_oneoff = this.poll_oneoff.bind(this)\n    this.sock_accept = this.sock_accept.bind(this)\n    this.sock_recv = this.sock_recv.bind(this)\n    this.sock_send = this.sock_send.bind(this)\n    this.sock_shutdown = this.sock_shutdown.bind(this)\n    this.random_get = this.random_get.bind(this)\n    this.sched_yield = this.sched_yield.bind(this)\n  }\n\n  // Helper methods\n\n  // this binds the wasm to this WASI implementation\n  setup(wasm) {\n    this.wasm = wasm\n  }\n\n  // this binds the wasm to this WASI implementation\n  // and calls it's main()'\n  start(wasm) {\n    this.setup(wasm)\n    try {\n      if (wasm._start) {\n        wasm._start()\n      }\n      return 0\n    } catch (e) {\n      if (e instanceof WASIProcExit) {\n        return e.code\n      }\n      throw e\n    }\n  }\n\n  // Standard input (for fd_read)\n  stdin() {\n    return new Uint8Array()\n  }\n\n  // Standard output handling (for fd_write)\n  stdout(buffer) {\n    const text = this.textDecoder.decode(buffer).replace(/\\n$/g, '')\n    if (text) console.log(text)\n  }\n\n  // Standard error handling (for fd_write)\n  stderr(buffer) {\n    const text = this.textDecoder.decode(buffer).replace(/\\n$/g, '')\n    if (text) console.error(text)\n  }\n\n  // Args functions\n  args_get(argvP, argvBufP) {\n    const view = new DataView(this.wasm.memory.buffer)\n    const mem = new Uint8Array(this.wasm.memory.buffer)\n\n    for (const arg of this.args) {\n      view.setUint32(argvP, argvBufP, true)\n      argvP += 4\n      const encoded = this.textEncoder.encode(arg)\n      mem.set(encoded, argvBufP)\n      mem[argvBufP + encoded.length] = 0 // null terminator\n      argvBufP += encoded.length + 1\n    }\n    return defs.ERRNO_SUCCESS\n  }\n\n  args_sizes_get(argcPtr, argvBufSizePtr) {\n    const view = new DataView(this.wasm.memory.buffer)\n    view.setUint32(argcPtr, this.args.length, true)\n    const bufSize = this.args.reduce((acc, arg) => acc + arg.length + 1, 0)\n    view.setUint32(argvBufSizePtr, bufSize, true)\n    return defs.ERRNO_SUCCESS\n  }\n\n  // Environment functions\n  environ_get(environP, environBufP) {\n    const view = new DataView(this.wasm.memory.buffer)\n    const mem = new Uint8Array(this.wasm.memory.buffer)\n\n    for (const [key, value] of Object.entries(this.env)) {\n      view.setUint32(environP, environBufP, true)\n      environP += 4\n      const entry = `${key}=${value}\\0`\n      const encoded = this.textEncoder.encode(entry)\n      mem.set(encoded, environBufP)\n      environBufP += encoded.length\n    }\n    return defs.ERRNO_SUCCESS\n  }\n\n  environ_sizes_get(environCountPtr, environBufSizePtr) {\n    const view = new DataView(this.wasm.memory.buffer)\n    const count = Object.keys(this.env).length\n    view.setUint32(environCountPtr, count, true)\n    const bufSize = Object.entries(this.env).reduce(\n      (acc, [k, v]) => acc + k.length + v.length + 2,\n      0,\n    )\n    view.setUint32(environBufSizePtr, bufSize, true)\n    return defs.ERRNO_SUCCESS\n  }\n\n  // Clock functions\n  clock_res_get(id, resPtr) {\n    const view = new DataView(this.wasm.memory.buffer)\n    let resolution\n    switch (id) {\n      case defs.CLOCKID_REALTIME:\n        resolution = 1_000_000n // 1ms in nanoseconds\n        break\n      case defs.CLOCKID_MONOTONIC:\n        resolution = 1_000n // 1μs in nanoseconds\n        break\n      default:\n        return defs.ERRNO_INVAL\n    }\n    view.setBigUint64(resPtr, resolution, true)\n    return defs.ERRNO_SUCCESS\n  }\n\n  clock_time_get(id, precision, timePtr) {\n    const view = new DataView(this.wasm.memory.buffer)\n    let time\n    switch (id) {\n      case defs.CLOCKID_REALTIME: {\n        const ms = Date.now()\n        time = BigInt(ms) * 1_000_000n\n        break\n      }\n      case defs.CLOCKID_MONOTONIC: {\n        const ns = BigInt(Math.round(performance.now() * 1_000_000))\n        time = ns\n        break\n      }\n      default:\n        return defs.ERRNO_INVAL\n    }\n    view.setBigUint64(timePtr, time, true)\n    return defs.ERRNO_SUCCESS\n  }\n\n  fd_close(fd) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    this.fds.delete(fd)\n    return defs.ERRNO_SUCCESS\n  }\n\n  // TODO: BIGINT\n  fd_seek(fd, offset, whence, newOffsetPtr) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    if (fileDesc.type === 'stdio') return defs.ERRNO_SPIPE\n\n    var stats = null\n    let newPosition = 0\n    let noffset = Number(offset)\n\n    try {\n      stats = this.fs.statSync(fileDesc.handle.path)\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n\n    switch (whence) {\n      case defs.WHENCE_SET:\n        newPosition = noffset\n        break\n      case defs.WHENCE_CUR:\n        newPosition = Number(fileDesc.handle.position) + noffset\n        break\n      case defs.WHENCE_END:\n        newPosition = Number(stats.size) + noffset\n        break\n      default:\n        console.error('fd_seek invalid mode', whence)\n        return defs.ERRNO_INVAL\n    }\n\n    // Update position\n    fileDesc.handle.position = newPosition\n\n    const view = new DataView(this.wasm.memory.buffer)\n    view.setBigUint64(newOffsetPtr, BigInt(newPosition), true)\n    return defs.ERRNO_SUCCESS\n  }\n\n  fd_write(fd, iovs, iovsLen, nwrittenPtr) {\n    let written = 0\n    const chunks = []\n    const view = new DataView(this.wasm.memory.buffer)\n    const mem = new Uint8Array(this.wasm.memory.buffer)\n\n    // Gather all the chunks from the vectors\n    for (let i = 0; i < iovsLen; i++) {\n      const ptr = iovs + i * 8\n      const buf = view.getUint32(ptr, true)\n      const bufLen = view.getUint32(ptr + 4, true)\n      chunks.push(new Uint8Array(mem.buffer, buf, bufLen))\n      written += bufLen\n    }\n\n    // Concatenate chunks if needed\n    let buffer\n    if (chunks.length === 1) {\n      buffer = chunks[0]\n    } else {\n      buffer = new Uint8Array(written)\n      let offset = 0\n      for (const chunk of chunks) {\n        buffer.set(chunk, offset)\n        offset += chunk.length\n      }\n    }\n\n    // Handle standard streams\n    if (fd === 1) {\n      this.stdout(buffer)\n    } else if (fd === 2) {\n      this.stderr(buffer)\n    } else {\n      const fileDesc = this.fds.get(fd)\n      if (!fileDesc) return defs.ERRNO_BADF\n\n      fileDesc.handle.position += written\n\n      try {\n        // Write using ZenFS path-based API\n        this.fs.writeFileSync(fileDesc.handle.path, buffer)\n      } catch (e) {\n        //console.error(\"fs.writeFileSync failed:\", fileDesc.handle.path)\n        return defs.ERRNO_IO\n      }\n    }\n    //console.log(\"fd_write end\", written)\n    view.setUint32(nwrittenPtr, written, true)\n    return defs.ERRNO_SUCCESS\n  }\n\n  fd_read(fd, iovs, iovsLen, nreadPtr) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    let totalRead = 0\n    const view = new DataView(this.wasm.memory.buffer)\n    const mem = new Uint8Array(this.wasm.memory.buffer)\n\n    try {\n      let content\n      if (fd === 0) {\n        content = this.stdin()\n      } else {\n        content = this.fs.readFileSync(fileDesc.handle.path)\n      }\n\n      for (let i = 0; i < iovsLen; i++) {\n        const ptr = iovs + i * 8\n        const buf = view.getUint32(ptr, true)\n        const bufLen = view.getUint32(ptr + 4, true)\n\n        const start = fileDesc.handle.position\n        const end = Math.min(start + bufLen, content.length)\n        const bytesToRead = end - start\n\n        if (bytesToRead <= 0) break\n\n        mem.set(new Uint8Array(content.slice(start, end)), buf)\n        totalRead += bytesToRead\n        fileDesc.handle.position += bytesToRead\n\n        if (bytesToRead < bufLen) break\n      }\n\n      view.setUint32(nreadPtr, totalRead, true)\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  path_open(\n    dirfd,\n    dirflags,\n    path,\n    pathLen,\n    oflags,\n    fsRightsBase,\n    fsRightsInheriting,\n    fdflags,\n    fdPtr,\n  ) {\n    var fileDesc = this.fds.get(dirfd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    const mem = new Uint8Array(this.wasm.memory.buffer)\n    const pathBuffer = mem.slice(path, path + pathLen)\n    const pathString = this.textDecoder.decode(pathBuffer)\n    let resolvedPath = pathString\n\n    var fd = 0\n    const view = new DataView(this.wasm.memory.buffer)\n\n    // Resolve path relative to the directory fd\n    if (fileDesc.preopenPath) {\n      if (pathString.startsWith('/')) {\n        resolvedPath = pathString.slice(1)\n      }\n      resolvedPath =\n        fileDesc.preopenPath +\n        (fileDesc.preopenPath.endsWith('/') ? '' : '/') +\n        resolvedPath\n    }\n\n    var exists = false\n    var stats = null\n    const o_create = (oflags & defs.OFLAGS_CREAT) == defs.OFLAGS_CREAT\n    const o_directory =\n      (oflags & defs.OFLAGS_DIRECTORY) == defs.OFLAGS_DIRECTORY\n    const o_exclusive = (oflags & defs.OFLAGS_EXCL) == defs.OFLAGS_EXCL\n    const o_truncate = (oflags & defs.OFLAGS_TRUNC) == defs.OFLAGS_TRUNC\n    try {\n      // Verify file exists\n      stats = this.fs.statSync(resolvedPath)\n      exists = true\n    } catch (e) {}\n\n    if (o_exclusive || o_truncate) {\n      if (o_exclusive && exists) {\n        // null\n        view.setUint32(fdPtr, fd, true)\n        return defs.ERRNO_EXIST\n      }\n    }\n\n    // Store path and initial position in handle TODO: could be BIGINT\n    // fd = this.allocateFd({ path: resolvedPath, position: 0 }, 'file')\n    const fileHandle = { path: resolvedPath, position: 0 }\n    const type = 'file'\n    fd = this.nextFd++\n    const descriptor = { type, handle: fileHandle, fd }\n    this.fds.set(fd, descriptor)\n\n    fileDesc = this.fds.get(fd)\n\n    // TODO: could be BIGINT\n    fileDesc.handle.position = 0\n\n    if (o_truncate) {\n      // TODO: could be BIGINT\n      fileDesc.handle.size = 0\n    }\n\n    // console.log(`path_open[${fd}] : ${resolvedPath} o_directory=${o_directory} exists=${exists} o_exclusive=${o_exclusive} o_create=${o_create} o_truncate=${o_truncate}`)\n    // if (stats)\n    // console.log(`path_open[${fd}] : ${fileDesc.handle.position} / ${stats.size}`)\n\n    //  o_directory - ERRNO_NOTDIR\n\n    //  ERRNO_NOENT\n\n    view.setUint32(fdPtr, fd, true)\n    return defs.ERRNO_SUCCESS\n  }\n\n  proc_exit(code) {\n    throw new WASIProcExit(code)\n  }\n\n  fd_fdstat_get(fd, statPtr) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    const view = new DataView(this.wasm.memory.buffer)\n\n    // filetype - u8\n    let filetype\n    switch (fileDesc.type) {\n      case 'stdio':\n        filetype = defs.FILETYPE_CHARACTER_DEVICE\n        break\n      case 'directory':\n        filetype = defs.FILETYPE_DIRECTORY\n        break\n      case 'file':\n        filetype = defs.FILETYPE_REGULAR_FILE\n        break\n      default:\n        filetype = defs.FILETYPE_UNKNOWN\n    }\n    view.setUint8(statPtr, filetype)\n\n    // fdflags - u16\n    // For now, we'll assume basic flags\n    let fdflags = 0\n    if (fileDesc.append) fdflags |= defs.FDFLAGS_APPEND\n    view.setUint16(statPtr + 2, fdflags, true)\n\n    // fs_rights_base - u64\n    // Set basic rights depending on file type\n    let fsRightsBase = 0n\n    if (fileDesc.type === 'file') {\n      fsRightsBase =\n        defs.RIGHTS_FD_READ |\n        defs.RIGHTS_FD_WRITE |\n        defs.RIGHTS_FD_SEEK |\n        defs.RIGHTS_FD_TELL |\n        defs.RIGHTS_FD_FILESTAT_GET\n    } else if (fileDesc.type === 'directory') {\n      fsRightsBase =\n        defs.RIGHTS_PATH_OPEN |\n        defs.RIGHTS_FD_READDIR |\n        defs.RIGHTS_PATH_CREATE_DIRECTORY |\n        defs.RIGHTS_PATH_UNLINK_FILE |\n        defs.RIGHTS_PATH_REMOVE_DIRECTORY\n    }\n    const bf = BigInt(fsRightsBase)\n    view.setBigUint64(statPtr + 8, bf, true)\n\n    // fs_rights_inheriting - u64\n    // Child files/directories inherit the same rights\n    view.setBigUint64(statPtr + 16, bf, true)\n\n    return defs.ERRNO_SUCCESS\n  }\n\n  fd_fdstat_set_flags(fd, flags) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    // Check if flags are valid\n    const validFlags =\n      defs.FDFLAGS_APPEND |\n      defs.FDFLAGS_DSYNC |\n      defs.FDFLAGS_NONBLOCK |\n      defs.FDFLAGS_RSYNC |\n      defs.FDFLAGS_SYNC\n\n    if (flags & ~validFlags) {\n      return defs.ERRNO_INVAL // Invalid flags specified\n    }\n\n    // For stdio handles, we can't set flags\n    if (fileDesc.type === 'stdio') {\n      return defs.ERRNO_NOTSUP\n    }\n\n    try {\n      // Update internal file descriptor state\n      fileDesc.append = Boolean(flags & defs.FDFLAGS_APPEND)\n\n      // Try to apply flags to the underlying file system\n      // Note: Many flags might not be supported by the underlying fs\n      if (fileDesc.handle && typeof this.fs.setFlagsSync === 'function') {\n        this.fs.setFlagsSync(fileDesc.handle, flags)\n      }\n\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  fd_prestat_get(fd, prestatPtr) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    // Only directory file descriptors have prestats\n    if (fileDesc.type !== 'directory') {\n      return defs.ERRNO_BADF\n    }\n\n    // Ensure we have a preopened path for this fd\n    if (!fileDesc.preopenPath) {\n      return defs.ERRNO_BADF\n    }\n\n    const view = new DataView(this.wasm.memory.buffer)\n\n    // Write prestat struct:\n    // struct prestat {\n    //   u8 type;    // offset 0\n    //   u64 length; // offset 8 (with padding)\n    // }\n\n    // Set type to PREOPENTYPE_DIR (0)\n    view.setUint8(prestatPtr, defs.PREOPENTYPE_DIR)\n\n    // Get the length of the preopened directory path\n    const pathLength = fileDesc.preopenPath.length\n    view.setUint32(prestatPtr + 4, pathLength, true)\n\n    return defs.ERRNO_SUCCESS\n  }\n\n  fd_prestat_dir_name(fd, pathPtr, pathLen) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    // Only directory file descriptors have prestats\n    if (fileDesc.type !== 'directory') {\n      return defs.ERRNO_BADF\n    }\n\n    // Ensure we have a preopened path for this fd\n    if (!fileDesc.preopenPath) {\n      return defs.ERRNO_BADF\n    }\n\n    // Check if the provided buffer is large enough\n    if (pathLen < fileDesc.preopenPath.length) {\n      return defs.ERRNO_NAMETOOLONG\n    }\n\n    // Write the path to memory\n    const mem = new Uint8Array(this.wasm.memory.buffer)\n    const pathBytes = this.textEncoder.encode(fileDesc.preopenPath)\n    mem.set(pathBytes, pathPtr)\n\n    return defs.ERRNO_SUCCESS\n  }\n\n  path_filestat_get(fd, flags, pathPtr, pathLen, filestatPtr) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    // Read the path from memory\n    const mem = new Uint8Array(this.wasm.memory.buffer)\n    const pathBytes = new Uint8Array(mem.buffer, pathPtr, pathLen)\n    const pathString = this.textDecoder.decode(pathBytes)\n\n    try {\n      // Resolve path relative to the directory fd\n      let resolvedPath = pathString\n      if (fileDesc.preopenPath) {\n        // If path starts with '/', make it relative to preopenPath\n        if (pathString.startsWith('/')) {\n          resolvedPath = pathString.slice(1) // Remove leading '/'\n        }\n        // Combine preopenPath with the relative path\n        resolvedPath =\n          fileDesc.preopenPath +\n          (fileDesc.preopenPath.endsWith('/') ? '' : '/') +\n          resolvedPath\n      }\n\n      // Get stats from filesystem\n      const stats = this.fs.statSync(resolvedPath, {\n        followSymlinks: (flags & defs.LOOKUPFLAGS_SYMLINK_FOLLOW) !== 0,\n      })\n\n      const view = new DataView(this.wasm.memory.buffer)\n\n      // Write filestat struct:\n      // struct filestat {\n      //   dev: u64,        // Device ID\n      //   ino: u64,        // Inode number\n      //   filetype: u8,    // File type\n      //   nlink: u64,      // Number of hard links\n      //   size: u64,       // File size\n      //   atim: u64,       // Access time\n      //   mtim: u64,       // Modification time\n      //   ctim: u64        // Change time\n      // }\n\n      // Device ID\n      view.setBigUint64(filestatPtr, BigInt(stats.dev || 0), true)\n\n      // Inode\n      view.setBigUint64(filestatPtr + 8, BigInt(stats.ino || 0), true)\n\n      // Filetype\n      let filetype = defs.FILETYPE_UNKNOWN\n      if (stats.isFile()) filetype = defs.FILETYPE_REGULAR_FILE\n      else if (stats.isDirectory()) filetype = defs.FILETYPE_DIRECTORY\n      else if (stats.isSymbolicLink()) filetype = defs.FILETYPE_SYMBOLIC_LINK\n      else if (stats.isCharacterDevice())\n        filetype = defs.FILETYPE_CHARACTER_DEVICE\n      else if (stats.isBlockDevice()) filetype = defs.FILETYPE_BLOCK_DEVICE\n      else if (stats.isFIFO()) filetype = defs.FILETYPE_SOCKET_STREAM\n      view.setUint8(filestatPtr + 16, filetype)\n\n      // Number of hard links\n      view.setBigUint64(filestatPtr + 24, BigInt(stats.nlink || 1), true)\n\n      // File size\n      view.setBigUint64(filestatPtr + 32, BigInt(stats.size || 0), true)\n\n      // Access time (in nanoseconds)\n      view.setBigUint64(\n        filestatPtr + 40,\n        BigInt(stats.atimeMs * 1_000_000),\n        true,\n      )\n\n      // Modification time (in nanoseconds)\n      view.setBigUint64(\n        filestatPtr + 48,\n        BigInt(stats.mtimeMs * 1_000_000),\n        true,\n      )\n\n      // Change time (in nanoseconds)\n      view.setBigUint64(\n        filestatPtr + 56,\n        BigInt(stats.ctimeMs * 1_000_000),\n        true,\n      )\n\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      if (e.code === 'ENOENT') return defs.ERRNO_NOENT\n      if (e.code === 'EACCES') return defs.ERRNO_ACCES\n      return defs.ERRNO_IO\n    }\n  }\n\n  // File/Directory Operations\n  fd_advise(fd, offset, len, advice) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    if (fileDesc.type !== 'file') return defs.ERRNO_BADF\n\n    // Most filesystems don't actually implement advisory hints,\n    // so we'll just return success\n    return defs.ERRNO_SUCCESS\n  }\n\n  fd_allocate(fd, offset, len) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    if (fileDesc.type !== 'file') return defs.ERRNO_BADF\n\n    try {\n      // Attempt to extend the file to the specified size\n      const stats = this.fs.statSync(fileDesc.handle.path)\n      const newSize = Number(offset) + Number(len)\n      if (newSize > stats.size) {\n        // Create a buffer of zeros to extend the file\n        const zeros = new Uint8Array(newSize - stats.size)\n        this.fs.appendFileSync(fileDesc.handle.path, zeros)\n      }\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  fd_datasync(fd) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    if (fileDesc.type !== 'file') return defs.ERRNO_BADF\n\n    try {\n      // Most JavaScript filesystem implementations handle syncing automatically\n      // If your fs implementation has a specific sync method, call it here\n      if (typeof this.fs.fsyncSync === 'function') {\n        this.fs.fsyncSync(fileDesc.handle.path)\n      }\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  fd_filestat_get(fd, ptr) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    if (!fileDesc.handle) return defs.ERRNO_BADF\n    const mem = new DataView(this.wasm.memory.buffer)\n    const stats = this.fs.statSync(fileDesc.handle.path)\n    mem.setBigUint64(ptr, BigInt(stats.dev), true)\n    mem.setBigUint64(ptr + 8, BigInt(stats.ino), true)\n    mem.setUint8(ptr + 16, stats.filetype)\n    mem.setBigUint64(ptr + 24, BigInt(stats.nlink), true)\n    mem.setBigUint64(ptr + 32, BigInt(stats.size), true)\n    mem.setBigUint64(ptr + 38, BigInt(stats.atime), true)\n    mem.setBigUint64(ptr + 46, BigInt(stats.mtime), true)\n    mem.setBigUint64(ptr + 52, BigInt(stats.ctime), true)\n    return defs.ERRNO_SUCCESS\n  }\n\n  fd_filestat_set_size(fd, size) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    if (fileDesc.type !== 'file') return defs.ERRNO_BADF\n\n    try {\n      this.fs.truncateSync(fileDesc.handle.path, Number(size))\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  fd_filestat_set_times(fd, atim, mtim, fst_flags) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    if (fileDesc.type !== 'file') return defs.ERRNO_BADF\n\n    try {\n      const times = {\n        atime: Number(atim) / 1_000_000_000,\n        mtime: Number(mtim) / 1_000_000_000,\n      }\n\n      this.fs.utimesSync(fileDesc.handle.path, times.atime, times.mtime)\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  fd_pread(fd, iovs, iovsLen, offset, nreadPtr) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    if (fileDesc.type !== 'file') return defs.ERRNO_BADF\n\n    try {\n      const content = this.fs.readFileSync(fileDesc.handle.path)\n      let totalRead = 0\n      const view = new DataView(this.wasm.memory.buffer)\n      const mem = new Uint8Array(this.wasm.memory.buffer)\n\n      const position = Number(offset)\n\n      for (let i = 0; i < iovsLen; i++) {\n        const ptr = iovs + i * 8\n        const buf = view.getUint32(ptr, true)\n        const bufLen = view.getUint32(ptr + 4, true)\n\n        const start = position + totalRead\n        const end = Math.min(start + bufLen, content.length)\n        const bytesToRead = end - start\n\n        if (bytesToRead <= 0) break\n\n        mem.set(new Uint8Array(content.slice(start, end)), buf)\n        totalRead += bytesToRead\n\n        if (bytesToRead < bufLen) break\n      }\n\n      view.setUint32(nreadPtr, totalRead, true)\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  fd_pwrite(fd, iovs, iovsLen, offset, nwrittenPtr) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    if (fileDesc.type !== 'file') return defs.ERRNO_BADF\n\n    try {\n      let written = 0\n      const chunks = []\n      const view = new DataView(this.wasm.memory.buffer)\n      const mem = new Uint8Array(this.wasm.memory.buffer)\n\n      for (let i = 0; i < iovsLen; i++) {\n        const ptr = iovs + i * 8\n        const buf = view.getUint32(ptr, true)\n        const bufLen = view.getUint32(ptr + 4, true)\n        chunks.push(new Uint8Array(mem.buffer, buf, bufLen))\n        written += bufLen\n      }\n\n      let buffer\n      if (chunks.length === 1) {\n        buffer = chunks[0]\n      } else {\n        buffer = new Uint8Array(written)\n        let offset = 0\n        for (const chunk of chunks) {\n          buffer.set(chunk, offset)\n          offset += chunk.length\n        }\n      }\n\n      // Read existing file content\n      const content = this.fs.readFileSync(fileDesc.handle.path)\n      const newContent = new Uint8Array(\n        Math.max(Number(offset) + buffer.length, content.length),\n      )\n\n      // Copy existing content\n      newContent.set(content)\n      // Write new data at specified offset\n      newContent.set(buffer, Number(offset))\n\n      // Write back to file\n      this.fs.writeFileSync(fileDesc.handle.path, newContent)\n\n      view.setUint32(nwrittenPtr, written, true)\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  fd_readdir(fd, buf, bufLen, cookie, bufusedPtr) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    if (fileDesc.type !== 'directory') return defs.ERRNO_NOTDIR\n\n    try {\n      const entries = this.fs.readdirSync(fileDesc.handle.path, {\n        withFileTypes: true,\n      })\n      const view = new DataView(this.wasm.memory.buffer)\n      const mem = new Uint8Array(this.wasm.memory.buffer)\n\n      let offset = 0\n      let entriesWritten = 0\n\n      // Skip entries according to cookie\n      const startIndex = Number(cookie)\n\n      for (let i = startIndex; i < entries.length; i++) {\n        const entry = entries[i]\n        const name = entry.name\n        const nameBytes = this.textEncoder.encode(name)\n\n        // dirent structure size: 24 bytes + name length\n        const direntSize = 24 + nameBytes.length\n\n        if (offset + direntSize > bufLen) {\n          break\n        }\n\n        // Write dirent structure\n        view.setBigUint64(buf + offset, BigInt(i + 1), true) // d_next\n        view.setBigUint64(buf + offset + 8, 0n, true) // d_ino\n        view.setUint32(buf + offset + 16, nameBytes.length, true) // d_namlen\n\n        // d_type\n        let filetype = defs.FILETYPE_UNKNOWN\n        if (entry.isFile()) filetype = defs.FILETYPE_REGULAR_FILE\n        else if (entry.isDirectory()) filetype = defs.FILETYPE_DIRECTORY\n        view.setUint8(buf + offset + 20, filetype)\n\n        // Write name\n        mem.set(nameBytes, buf + offset + 24)\n\n        offset += direntSize\n        entriesWritten++\n      }\n\n      view.setUint32(bufusedPtr, offset, true)\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  fd_renumber(from, to) {\n    const fromDesc = this.fds.get(from)\n    if (!fromDesc) return defs.ERRNO_BADF\n\n    // Close existing 'to' fd if it exists\n    this.fds.delete(to)\n\n    // Move the fd\n    this.fds.set(to, fromDesc)\n    this.fds.delete(from)\n\n    return defs.ERRNO_SUCCESS\n  }\n\n  fd_sync(fd) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    if (fileDesc.type !== 'file') return defs.ERRNO_BADF\n\n    try {\n      // Similar to fd_datasync, but might include metadata\n      if (typeof this.fs.fsyncSync === 'function') {\n        this.fs.fsyncSync(fileDesc.handle.path)\n      }\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  fd_tell(fd, offsetPtr) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n    if (fileDesc.type !== 'file') return defs.ERRNO_BADF\n\n    const view = new DataView(this.wasm.memory.buffer)\n    view.setBigUint64(offsetPtr, BigInt(fileDesc.handle.position), true)\n    return defs.ERRNO_SUCCESS\n  }\n\n  // Path Operations\n  path_create_directory(fd, path, pathLen) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    const pathString = this.textDecoder.decode(\n      new Uint8Array(this.wasm.memory.buffer, path, pathLen),\n    )\n\n    try {\n      let resolvedPath = pathString\n      if (fileDesc.preopenPath) {\n        if (pathString.startsWith('/')) {\n          resolvedPath = pathString.slice(1)\n        }\n        resolvedPath = fileDesc.preopenPath + '/' + resolvedPath\n      }\n\n      this.fs.mkdirSync(resolvedPath)\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  path_filestat_set_times(fd, flags, path, pathLen, atim, mtim, fst_flags) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    const pathString = this.textDecoder.decode(\n      new Uint8Array(this.wasm.memory.buffer, path, pathLen),\n    )\n\n    try {\n      let resolvedPath = pathString\n      if (fileDesc.preopenPath) {\n        if (pathString.startsWith('/')) {\n          resolvedPath = pathString.slice(1)\n        }\n        resolvedPath = fileDesc.preopenPath + '/' + resolvedPath\n      }\n\n      const times = {\n        atime: Number(atim) / 1_000_000_000,\n        mtime: Number(mtim) / 1_000_000_000,\n      }\n\n      this.fs.utimesSync(resolvedPath, times.atime, times.mtime)\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  path_link(\n    old_fd,\n    old_flags,\n    old_path,\n    old_path_len,\n    new_fd,\n    new_path,\n    new_path_len,\n  ) {\n    const oldFileDesc = this.fds.get(old_fd)\n    const newFileDesc = this.fds.get(new_fd)\n    if (!oldFileDesc || !newFileDesc) return defs.ERRNO_BADF\n\n    const oldPathString = this.textDecoder.decode(\n      new Uint8Array(this.wasm.memory.buffer, old_path, old_path_len),\n    )\n    const newPathString = this.textDecoder.decode(\n      new Uint8Array(this.wasm.memory.buffer, new_path, new_path_len),\n    )\n\n    try {\n      let resolvedOldPath = oldPathString\n      let resolvedNewPath = newPathString\n\n      if (oldFileDesc.preopenPath) {\n        if (oldPathString.startsWith('/')) {\n          resolvedOldPath = oldPathString.slice(1)\n        }\n        resolvedOldPath = oldFileDesc.preopenPath + '/' + resolvedOldPath\n      }\n\n      if (newFileDesc.preopenPath) {\n        if (newPathString.startsWith('/')) {\n          resolvedNewPath = newPathString.slice(1)\n        }\n        resolvedNewPath = newFileDesc.preopenPath + '/' + resolvedNewPath\n      }\n\n      this.fs.linkSync(resolvedOldPath, resolvedNewPath)\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  path_readlink(fd, path, path_len, buf, buf_len, bufused) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    const pathString = this.textDecoder.decode(\n      new Uint8Array(this.wasm.memory.buffer, path, path_len),\n    )\n\n    try {\n      let resolvedPath = pathString\n      if (fileDesc.preopenPath) {\n        if (pathString.startsWith('/')) {\n          resolvedPath = pathString.slice(1)\n        }\n        resolvedPath = fileDesc.preopenPath + '/' + resolvedPath\n      }\n\n      const linkString = this.fs.readlinkSync(resolvedPath)\n      const linkBytes = this.textEncoder.encode(linkString)\n\n      if (linkBytes.length > buf_len) {\n        return defs.ERRNO_OVERFLOW\n      }\n\n      const mem = new Uint8Array(this.wasm.memory.buffer)\n      mem.set(linkBytes, buf)\n\n      const view = new DataView(this.wasm.memory.buffer)\n      view.setUint32(bufused, linkBytes.length, true)\n\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  path_remove_directory(fd, path, path_len) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    const pathString = this.textDecoder.decode(\n      new Uint8Array(this.wasm.memory.buffer, path, path_len),\n    )\n\n    try {\n      let resolvedPath = pathString\n      if (fileDesc.preopenPath) {\n        if (pathString.startsWith('/')) {\n          resolvedPath = pathString.slice(1)\n        }\n        resolvedPath = fileDesc.preopenPath + '/' + resolvedPath\n      }\n\n      this.fs.rmdirSync(resolvedPath)\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  path_rename(old_fd, old_path, old_path_len, new_fd, new_path, new_path_len) {\n    const oldFileDesc = this.fds.get(old_fd)\n    const newFileDesc = this.fds.get(new_fd)\n    if (!oldFileDesc || !newFileDesc) return defs.ERRNO_BADF\n\n    const oldPathString = this.textDecoder.decode(\n      new Uint8Array(this.wasm.memory.buffer, old_path, old_path_len),\n    )\n    const newPathString = this.textDecoder.decode(\n      new Uint8Array(this.wasm.memory.buffer, new_path, new_path_len),\n    )\n\n    try {\n      let resolvedOldPath = oldPathString\n      let resolvedNewPath = newPathString\n\n      if (oldFileDesc.preopenPath) {\n        if (oldPathString.startsWith('/')) {\n          resolvedOldPath = oldPathString.slice(1)\n        }\n        resolvedOldPath = oldFileDesc.preopenPath + '/' + resolvedOldPath\n      }\n\n      if (newFileDesc.preopenPath) {\n        if (newPathString.startsWith('/')) {\n          resolvedNewPath = newPathString.slice(1)\n        }\n        resolvedNewPath = newFileDesc.preopenPath + '/' + resolvedNewPath\n      }\n\n      this.fs.renameSync(resolvedOldPath, resolvedNewPath)\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  path_symlink(old_path, old_path_len, fd, new_path, new_path_len) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    const oldPathString = this.textDecoder.decode(\n      new Uint8Array(this.wasm.memory.buffer, old_path, old_path_len),\n    )\n    const newPathString = this.textDecoder.decode(\n      new Uint8Array(this.wasm.memory.buffer, new_path, new_path_len),\n    )\n\n    try {\n      let resolvedNewPath = newPathString\n      if (fileDesc.preopenPath) {\n        if (newPathString.startsWith('/')) {\n          resolvedNewPath = newPathString.slice(1)\n        }\n        resolvedNewPath = fileDesc.preopenPath + '/' + resolvedNewPath\n      }\n\n      this.fs.symlinkSync(oldPathString, resolvedNewPath)\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  path_unlink_file(fd, path, path_len) {\n    const fileDesc = this.fds.get(fd)\n    if (!fileDesc) return defs.ERRNO_BADF\n\n    const pathString = this.textDecoder.decode(\n      new Uint8Array(this.wasm.memory.buffer, path, path_len),\n    )\n\n    try {\n      let resolvedPath = pathString\n      if (fileDesc.preopenPath) {\n        if (pathString.startsWith('/')) {\n          resolvedPath = pathString.slice(1)\n        }\n        resolvedPath = fileDesc.preopenPath + '/' + resolvedPath\n      }\n\n      this.fs.unlinkSync(resolvedPath)\n      return defs.ERRNO_SUCCESS\n    } catch (e) {\n      return defs.ERRNO_IO\n    }\n  }\n\n  // Poll Operations\n  poll_oneoff(in_, out, nsubscriptions, nevents) {\n    // Basic implementation that just processes all subscriptions immediately\n    const view = new DataView(this.wasm.memory.buffer)\n    let numEvents = 0\n\n    for (let i = 0; i < nsubscriptions; i++) {\n      const subPtr = in_ + i * 48 // size of subscription struct\n      const userdata = view.getBigUint64(subPtr, true)\n      const type = view.getUint8(subPtr + 8)\n\n      // Write event\n      const eventPtr = out + numEvents * 32 // size of event struct\n      view.setBigUint64(eventPtr, userdata, true)\n      view.setUint8(eventPtr + 8, type)\n      view.setUint8(eventPtr + 9, defs.EVENTRWFLAGS_FD_READWRITE_HANGUP)\n      view.setUint16(eventPtr + 10, 0, true) // error\n\n      numEvents++\n    }\n\n    view.setUint32(nevents, numEvents, true)\n    return defs.ERRNO_SUCCESS\n  }\n\n  // Random Number Generation\n  random_get(buf, buf_len) {\n    const bytes = new Uint8Array(this.wasm.memory.buffer, buf, buf_len)\n    crypto.getRandomValues(bytes)\n    return defs.ERRNO_SUCCESS\n  }\n\n  // Scheduling Operations\n  sched_yield() {\n    os.sched_yield()\n    return defs.ERRNO_SUCCESS\n  }\n\n  // STUB\n  sock_accept(fd, flags) {\n    return defs.ERRNO_NOSYS\n  }\n\n  sock_recv(fd, riData, riFlags) {\n    return defs.ERRNO_NOSYS\n  }\n\n  sock_send(fd, siData, riFlags) {\n    return defs.ERRNO_NOSYS\n  }\n\n  sock_shutdown(fd, how) {\n    return defs.ERRNO_NOSYS\n  }\n}\n\nexport default WasiPreview1\n", "import { WasiPreview1 } from './wasi/easywasi'\nimport { postgresMod } from '@electric-sql/pglite'\nimport { PGlite } from '@electric-sql/pglite'\n\ntype FS = postgresMod.FS\ntype FSInterface = any // WASI FS interface\n\nconst IN_NODE =\n  typeof process === 'object' &&\n  typeof process.versions === 'object' &&\n  typeof process.versions.node === 'string'\n\n/**\n * Emscripten FS is not quite compatible with WASI\n * so we need to patch it\n */\nfunction emscriptenFsToWasiFS(fs: FS, acc: any[]): FSInterface & FS {\n  const requiredMethods = [\n    'appendFileSync',\n    'fsyncSync',\n    'linkSync',\n    'setFlagsSync',\n    'mkdirSync',\n    'readdirSync',\n    'readFileSync',\n    'readlinkSync',\n    'renameSync',\n    'rmdirSync',\n    'statSync',\n    'symlinkSync',\n    'truncateSync',\n    'unlinkSync',\n    'utimesSync',\n    'writeFileSync',\n  ]\n  return {\n    // Bind all methods to the FS instance\n    ...fs,\n    // Add missing methods\n    ...Object.fromEntries(\n      requiredMethods\n        .map((method) => {\n          const target = method.slice(0, method.length - 4)\n          if (!(target in fs)) {\n            return [\n              method,\n              () => {\n                throw new Error(`${target} not implemented.`)\n              },\n            ]\n          }\n          return [\n            method,\n            (...args: any[]) => {\n              if (method === 'writeFileSync' && args[0] === '/tmp/out.sql') {\n                acc.push(args[1])\n              }\n              return (fs as any)[target](...args)\n            },\n          ]\n        })\n        .filter(\n          (entry): entry is [string, (fs: FS) => any] => entry !== undefined,\n        ),\n    ),\n  } as FSInterface & FS\n}\n\n/**\n * Inner function to execute pg_dump\n */\nasync function execPgDump({\n  pg,\n  args,\n}: {\n  pg: PGlite\n  args: string[]\n}): Promise<[number, Uint8Array[], string]> {\n  const bin = new URL('./pg_dump.wasm', import.meta.url)\n  const acc: Uint8Array[] = []\n  const FS = emscriptenFsToWasiFS(pg.Module.FS, acc)\n\n  const wasi = new WasiPreview1({\n    fs: FS,\n    args: ['pg_dump', ...args],\n    env: {\n      PWD: '/',\n    },\n  })\n\n  wasi.stdout = (_buffer) => {\n    // console.log('stdout', _buffer)\n  }\n  const textDecoder = new TextDecoder()\n  let errorMessage = ''\n\n  wasi.stderr = (_buffer) => {\n    const text = textDecoder.decode(_buffer)\n    if (text) errorMessage += text\n  }\n  wasi.sched_yield = () => {\n    const pgIn = '/tmp/pglite/base/.s.PGSQL.5432.in'\n    const pgOut = '/tmp/pglite/base/.s.PGSQL.5432.out'\n    if (FS.analyzePath(pgIn).exists) {\n      // call interactive one\n      const msgIn = FS.readFileSync(pgIn)\n\n      // BYPASS the file socket emulation in PGlite\n      FS.unlinkSync(pgIn)\n\n      // Handle auth request\n      if (msgIn[0] === 0) {\n        const reply = new Uint8Array([\n          ...authOk,\n          ...versionParam,\n          ...readyForQuery,\n        ])\n        FS.writeFileSync(pgOut, reply)\n        return 0\n      }\n\n      // Handle query\n      const reply = pg.execProtocolRawSync(msgIn)\n      FS.writeFileSync(pgOut, reply)\n    }\n    return 0\n  }\n\n  // Postgres can complain if the binary is not on the filesystem\n  // so we create a dummy file\n  await FS.writeFile('/pg_dump', '\\0', { mode: 18 })\n\n  let app: WebAssembly.WebAssemblyInstantiatedSource\n\n  if (IN_NODE) {\n    const fs = await import('fs/promises')\n    const blob = await fs.readFile(bin.toString().slice(7))\n    app = await WebAssembly.instantiate(blob, {\n      wasi_snapshot_preview1: wasi as any,\n    })\n  } else {\n    app = await WebAssembly.instantiateStreaming(fetch(bin), {\n      wasi_snapshot_preview1: wasi as any,\n    })\n  }\n\n  let exitCode: number\n  await pg.runExclusive(async () => {\n    exitCode = wasi.start(app.instance.exports)\n  })\n  return [exitCode!, acc, errorMessage]\n}\n\ninterface PgDumpOptions {\n  pg: PGlite\n  args?: string[]\n  fileName?: string\n}\n\n/**\n * Execute pg_dump\n */\nexport async function pgDump({\n  pg,\n  args,\n  fileName = 'dump.sql',\n}: PgDumpOptions) {\n  const getSearchPath = await pg.query<{ search_path: string }>(\n    'SHOW SEARCH_PATH;',\n  )\n  const search_path = getSearchPath.rows[0].search_path\n\n  const outFile = `/tmp/out.sql`\n  const baseArgs = [\n    '-U',\n    'postgres',\n    '--inserts',\n    '-j',\n    '1',\n    '-f',\n    outFile,\n    'postgres',\n  ]\n\n  const [exitCode, acc, errorMessage] = await execPgDump({\n    pg,\n    args: [...(args ?? []), ...baseArgs],\n  })\n\n  pg.exec(`DEALLOCATE ALL; SET SEARCH_PATH = ${search_path}`)\n\n  if (exitCode !== 0) {\n    throw new Error(\n      `pg_dump failed with exit code ${exitCode}. \\nError message: ${errorMessage}`,\n    )\n  }\n\n  const file = new File(acc, fileName, {\n    type: 'text/plain',\n  })\n  pg.Module.FS.unlink(outFile)\n\n  return file\n}\n\n// Wire protocol messages for simulating auth handshake:\n\nfunction charToByte(char: string) {\n  return char.charCodeAt(0)\n}\n\n// Function to convert an integer to a 4-byte array (Int32)\nfunction int32ToBytes(value: number) {\n  const buffer = new ArrayBuffer(4)\n  const view = new DataView(buffer)\n  view.setInt32(0, value, false) // false for big-endian\n  return new Uint8Array(buffer)\n}\n\n// Convert a string to a Uint8Array with a null terminator (C string)\nfunction stringToBytes(str: string) {\n  const utf8Encoder = new TextEncoder()\n  const strBytes = utf8Encoder.encode(str) // UTF-8 encoding\n  return new Uint8Array([...strBytes, 0]) // Append null terminator\n}\n\nconst authOk = new Uint8Array([\n  charToByte('R'),\n  ...int32ToBytes(8),\n  ...int32ToBytes(0),\n])\nconst readyForQuery = new Uint8Array([\n  charToByte('Z'),\n  ...int32ToBytes(5),\n  charToByte('I'),\n])\n\nconst svParamName = stringToBytes('server_version')\nconst svParamValue = stringToBytes('16.3 (PGlite 0.2.0)')\nconst svTotalLength = 4 + svParamName.length + svParamValue.length\nconst versionParam = new Uint8Array([\n  charToByte('S'),\n  ...int32ToBytes(svTotalLength),\n  ...svParamName,\n  ...svParamValue,\n])\n"], "mappings": "AAQO,IAAMA,EAAN,cAA2B,KAAM,CACtC,YAAYC,EAAM,CAChB,MAAM,kBAAkBA,CAAI,EAAE,EAC9B,KAAK,KAAOA,CACd,CACF,EAGaC,EAAN,KAAc,CACnB,eAAeC,EAAMC,EAAMC,EAAU,CAAC,EAAG,CACvC,MAAM,IAAI,MAAM,gCAAgC,CAClD,CAEA,UAAUC,EAAI,CACZ,MAAM,IAAI,MAAM,2BAA2B,CAC7C,CAEA,SAASC,EAAcC,EAAS,CAC9B,MAAM,IAAI,MAAM,0BAA0B,CAC5C,CAEA,UAAUL,EAAME,EAAU,CAAC,EAAG,CAC5B,MAAM,IAAI,MAAM,2BAA2B,CAC7C,CAEA,YAAYF,EAAME,EAAU,CAAC,EAAG,CAC9B,MAAM,IAAI,MAAM,6BAA6B,CAC/C,CAEA,aAAaF,EAAME,EAAU,CAAC,EAAG,CAC/B,MAAM,IAAI,MAAM,8BAA8B,CAChD,CAEA,aAAaF,EAAME,EAAU,CAAC,EAAG,CAC/B,MAAM,IAAI,MAAM,8BAA8B,CAChD,CAEA,WAAWI,EAASD,EAAS,CAC3B,MAAM,IAAI,MAAM,4BAA4B,CAC9C,CAEA,UAAUL,EAAME,EAAU,CAAC,EAAG,CAC5B,MAAM,IAAI,MAAM,2BAA2B,CAC7C,CAEA,aAAaF,EAAMO,EAAO,CACxB,MAAM,IAAI,MAAM,8BAA8B,CAChD,CAEA,SAASP,EAAME,EAAU,CAAC,EAAG,CAC3B,MAAM,IAAI,MAAM,0BAA0B,CAC5C,CAEA,YAAYM,EAAQR,EAAMS,EAAO,OAAQ,CACvC,MAAM,IAAI,MAAM,6BAA6B,CAC/C,CAEA,aAAaT,EAAMU,EAAM,EAAG,CAC1B,MAAM,IAAI,MAAM,8BAA8B,CAChD,CAEA,WAAWV,EAAM,CACf,MAAM,IAAI,MAAM,4BAA4B,CAC9C,CAEA,WAAWA,EAAMW,EAAOC,EAAO,CAC7B,MAAM,IAAI,MAAM,4BAA4B,CAC9C,CAEA,cAAcZ,EAAMC,EAAMC,EAAU,CAAC,EAAG,CACtC,MAAM,IAAI,MAAM,+BAA+B,CACjD,CACF,EAEaW,EAAN,KAAmB,CACxB,YAAYX,EAAU,CAAC,EAAG,CAKxB,GAJA,KAAK,KAAOA,EAAQ,MAAQ,CAAC,EAC7B,KAAK,IAAMA,EAAQ,KAAO,CAAC,EAC3B,KAAK,GAAKA,EAAQ,IAAM,IAAIH,EAExB,CAAC,KAAK,GACR,MAAM,IAAI,MAAM,qCAAqC,EAKvD,KAAK,IAAM,IAAI,IAAI,CACjB,CAAC,EAAG,CAAE,KAAM,OAAQ,CAAC,EACrB,CAAC,EAAG,CAAE,KAAM,OAAQ,CAAC,EACrB,CAAC,EAAG,CAAE,KAAM,OAAQ,CAAC,EACrB,CAAC,EAAG,CAAE,KAAM,YAAa,YAAa,GAAI,CAAC,CAC7C,CAAC,EAED,KAAK,OAAS,KAAK,IAAI,KACvB,KAAK,YAAc,IAAI,YACvB,KAAK,YAAc,IAAI,YAGvB,KAAK,SAAW,KAAK,SAAS,KAAK,IAAI,EACvC,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EACnD,KAAK,YAAc,KAAK,YAAY,KAAK,IAAI,EAC7C,KAAK,kBAAoB,KAAK,kBAAkB,KAAK,IAAI,EACzD,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EACnD,KAAK,SAAW,KAAK,SAAS,KAAK,IAAI,EACvC,KAAK,QAAU,KAAK,QAAQ,KAAK,IAAI,EACrC,KAAK,SAAW,KAAK,SAAS,KAAK,IAAI,EACvC,KAAK,QAAU,KAAK,QAAQ,KAAK,IAAI,EACrC,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,oBAAsB,KAAK,oBAAoB,KAAK,IAAI,EAC7D,KAAK,eAAiB,KAAK,eAAe,KAAK,IAAI,EACnD,KAAK,oBAAsB,KAAK,oBAAoB,KAAK,IAAI,EAC7D,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,EACzC,KAAK,kBAAoB,KAAK,kBAAkB,KAAK,IAAI,EACzD,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,EACzC,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,EACzC,KAAK,YAAc,KAAK,YAAY,KAAK,IAAI,EAC7C,KAAK,YAAc,KAAK,YAAY,KAAK,IAAI,EAC7C,KAAK,gBAAkB,KAAK,gBAAgB,KAAK,IAAI,EACrD,KAAK,qBAAuB,KAAK,qBAAqB,KAAK,IAAI,EAC/D,KAAK,sBAAwB,KAAK,sBAAsB,KAAK,IAAI,EACjE,KAAK,SAAW,KAAK,SAAS,KAAK,IAAI,EACvC,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,EACzC,KAAK,WAAa,KAAK,WAAW,KAAK,IAAI,EAC3C,KAAK,YAAc,KAAK,YAAY,KAAK,IAAI,EAC7C,KAAK,QAAU,KAAK,QAAQ,KAAK,IAAI,EACrC,KAAK,QAAU,KAAK,QAAQ,KAAK,IAAI,EACrC,KAAK,sBAAwB,KAAK,sBAAsB,KAAK,IAAI,EACjE,KAAK,wBAA0B,KAAK,wBAAwB,KAAK,IAAI,EACrE,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,EACzC,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,sBAAwB,KAAK,sBAAsB,KAAK,IAAI,EACjE,KAAK,YAAc,KAAK,YAAY,KAAK,IAAI,EAC7C,KAAK,aAAe,KAAK,aAAa,KAAK,IAAI,EAC/C,KAAK,iBAAmB,KAAK,iBAAiB,KAAK,IAAI,EACvD,KAAK,YAAc,KAAK,YAAY,KAAK,IAAI,EAC7C,KAAK,YAAc,KAAK,YAAY,KAAK,IAAI,EAC7C,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,EACzC,KAAK,UAAY,KAAK,UAAU,KAAK,IAAI,EACzC,KAAK,cAAgB,KAAK,cAAc,KAAK,IAAI,EACjD,KAAK,WAAa,KAAK,WAAW,KAAK,IAAI,EAC3C,KAAK,YAAc,KAAK,YAAY,KAAK,IAAI,CAC/C,CAKA,MAAMe,EAAM,CACV,KAAK,KAAOA,CACd,CAIA,MAAMA,EAAM,CACV,KAAK,MAAMA,CAAI,EACf,GAAI,CACF,OAAIA,EAAK,QACPA,EAAK,OAAO,EAEP,CACT,OAASC,EAAG,CACV,GAAIA,aAAalB,EACf,OAAOkB,EAAE,KAEX,MAAMA,CACR,CACF,CAGA,OAAQ,CACN,OAAO,IAAI,UACb,CAGA,OAAOC,EAAQ,CACb,IAAMC,EAAO,KAAK,YAAY,OAAOD,CAAM,EAAE,QAAQ,OAAQ,EAAE,EAC3DC,GAAM,QAAQ,IAAIA,CAAI,CAC5B,CAGA,OAAOD,EAAQ,CACb,IAAMC,EAAO,KAAK,YAAY,OAAOD,CAAM,EAAE,QAAQ,OAAQ,EAAE,EAC3DC,GAAM,QAAQ,MAAMA,CAAI,CAC9B,CAGA,SAASC,EAAOC,EAAU,CACxB,IAAMC,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC3CC,EAAM,IAAI,WAAW,KAAK,KAAK,OAAO,MAAM,EAElD,QAAWC,KAAO,KAAK,KAAM,CAC3BF,EAAK,UAAUF,EAAOC,EAAU,EAAI,EACpCD,GAAS,EACT,IAAMK,EAAU,KAAK,YAAY,OAAOD,CAAG,EAC3CD,EAAI,IAAIE,EAASJ,CAAQ,EACzBE,EAAIF,EAAWI,EAAQ,MAAM,EAAI,EACjCJ,GAAYI,EAAQ,OAAS,CAC/B,CACA,MAAY,EACd,CAEA,eAAeC,EAASC,EAAgB,CACtC,IAAML,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EACjDA,EAAK,UAAUI,EAAS,KAAK,KAAK,OAAQ,EAAI,EAC9C,IAAME,EAAU,KAAK,KAAK,OAAO,CAACC,EAAKL,IAAQK,EAAML,EAAI,OAAS,EAAG,CAAC,EACtE,OAAAF,EAAK,UAAUK,EAAgBC,EAAS,EAAI,EAChC,CACd,CAGA,YAAYE,EAAUC,EAAa,CACjC,IAAMT,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC3CC,EAAM,IAAI,WAAW,KAAK,KAAK,OAAO,MAAM,EAElD,OAAW,CAACS,EAAKC,CAAK,IAAK,OAAO,QAAQ,KAAK,GAAG,EAAG,CACnDX,EAAK,UAAUQ,EAAUC,EAAa,EAAI,EAC1CD,GAAY,EACZ,IAAMI,EAAQ,GAAGF,CAAG,IAAIC,CAAK,KACvBR,EAAU,KAAK,YAAY,OAAOS,CAAK,EAC7CX,EAAI,IAAIE,EAASM,CAAW,EAC5BA,GAAeN,EAAQ,MACzB,CACA,MAAY,EACd,CAEA,kBAAkBU,EAAiBC,EAAmB,CACpD,IAAMd,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC3Ce,EAAQ,OAAO,KAAK,KAAK,GAAG,EAAE,OACpCf,EAAK,UAAUa,EAAiBE,EAAO,EAAI,EAC3C,IAAMT,EAAU,OAAO,QAAQ,KAAK,GAAG,EAAE,OACvC,CAACC,EAAK,CAACS,EAAGC,CAAC,IAAMV,EAAMS,EAAE,OAASC,EAAE,OAAS,EAC7C,CACF,EACA,OAAAjB,EAAK,UAAUc,EAAmBR,EAAS,EAAI,EACnC,CACd,CAGA,cAAcY,EAAIC,EAAQ,CACxB,IAAMnB,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC7CoB,EACJ,OAAQF,EAAI,CACV,IAAU,GACRE,EAAa,SACb,MACF,IAAU,GACRA,EAAa,MACb,MACF,QACE,MAAY,GAChB,CACA,OAAApB,EAAK,aAAamB,EAAQC,EAAY,EAAI,EAC9B,CACd,CAEA,eAAeF,EAAIG,EAAWC,EAAS,CACrC,IAAMtB,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC7CuB,EACJ,OAAQL,EAAI,CACV,IAAU,GAAkB,CAC1B,IAAMM,EAAK,KAAK,IAAI,EACpBD,EAAO,OAAOC,CAAE,EAAI,SACpB,KACF,CACA,IAAU,GAAmB,CAE3BD,EADW,OAAO,KAAK,MAAM,YAAY,IAAI,EAAI,GAAS,CAAC,EAE3D,KACF,CACA,QACE,MAAY,GAChB,CACA,OAAAvB,EAAK,aAAasB,EAASC,EAAM,EAAI,EACzB,CACd,CAEA,SAASxC,EAAI,CAEX,OADiB,KAAK,IAAI,IAAIA,CAAE,GAEhC,KAAK,IAAI,OAAOA,CAAE,EACN,GAFe,CAG7B,CAGA,QAAQA,EAAI0C,EAAQC,EAAQC,EAAc,CACxC,IAAMC,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAC3B,GAAIA,EAAS,OAAS,QAAS,MAAY,IAE3C,IAAIC,EAAQ,KACZ,IAAIC,EAAc,EACdC,EAAU,OAAON,CAAM,EAE3B,GAAI,CACFI,EAAQ,KAAK,GAAG,SAASD,EAAS,OAAO,IAAI,CAC/C,MAAY,CACV,MAAY,GACd,CAEA,OAAQF,EAAQ,CACd,IAAU,GACRI,EAAcC,EACd,MACF,IAAU,GACRD,EAAc,OAAOF,EAAS,OAAO,QAAQ,EAAIG,EACjD,MACF,IAAU,GACRD,EAAc,OAAOD,EAAM,IAAI,EAAIE,EACnC,MACF,QACE,eAAQ,MAAM,uBAAwBL,CAAM,EAChC,EAChB,CAGA,OAAAE,EAAS,OAAO,SAAWE,EAEd,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC5C,aAAaH,EAAc,OAAOG,CAAW,EAAG,EAAI,EAC7C,CACd,CAEA,SAAS/C,EAAIiD,EAAMC,EAASC,EAAa,CACvC,IAAIC,EAAU,EACRC,EAAS,CAAC,EACVpC,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC3CC,EAAM,IAAI,WAAW,KAAK,KAAK,OAAO,MAAM,EAGlD,QAASoC,EAAI,EAAGA,EAAIJ,EAASI,IAAK,CAChC,IAAMC,EAAMN,EAAOK,EAAI,EACjBE,EAAMvC,EAAK,UAAUsC,EAAK,EAAI,EAC9BE,EAASxC,EAAK,UAAUsC,EAAM,EAAG,EAAI,EAC3CF,EAAO,KAAK,IAAI,WAAWnC,EAAI,OAAQsC,EAAKC,CAAM,CAAC,EACnDL,GAAWK,CACb,CAGA,IAAI5C,EACJ,GAAIwC,EAAO,SAAW,EACpBxC,EAASwC,EAAO,CAAC,MACZ,CACLxC,EAAS,IAAI,WAAWuC,CAAO,EAC/B,IAAIV,EAAS,EACb,QAAWgB,KAASL,EAClBxC,EAAO,IAAI6C,EAAOhB,CAAM,EACxBA,GAAUgB,EAAM,MAEpB,CAGA,GAAI1D,IAAO,EACT,KAAK,OAAOa,CAAM,UACTb,IAAO,EAChB,KAAK,OAAOa,CAAM,MACb,CACL,IAAMgC,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAE3BA,EAAS,OAAO,UAAYO,EAE5B,GAAI,CAEF,KAAK,GAAG,cAAcP,EAAS,OAAO,KAAMhC,CAAM,CACpD,MAAY,CAEV,MAAY,GACd,CACF,CAEA,OAAAI,EAAK,UAAUkC,EAAaC,EAAS,EAAI,EAC7B,CACd,CAEA,QAAQpD,EAAIiD,EAAMC,EAASS,EAAU,CACnC,IAAMd,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAE3B,IAAIe,EAAY,EACV3C,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC3CC,EAAM,IAAI,WAAW,KAAK,KAAK,OAAO,MAAM,EAElD,GAAI,CACF,IAAI2C,EACA7D,IAAO,EACT6D,EAAU,KAAK,MAAM,EAErBA,EAAU,KAAK,GAAG,aAAahB,EAAS,OAAO,IAAI,EAGrD,QAASS,EAAI,EAAGA,EAAIJ,EAASI,IAAK,CAChC,IAAMC,EAAMN,EAAOK,EAAI,EACjBE,EAAMvC,EAAK,UAAUsC,EAAK,EAAI,EAC9BE,EAASxC,EAAK,UAAUsC,EAAM,EAAG,EAAI,EAErCO,EAAQjB,EAAS,OAAO,SACxBkB,EAAM,KAAK,IAAID,EAAQL,EAAQI,EAAQ,MAAM,EAC7CG,EAAcD,EAAMD,EAQ1B,GANIE,GAAe,IAEnB9C,EAAI,IAAI,IAAI,WAAW2C,EAAQ,MAAMC,EAAOC,CAAG,CAAC,EAAGP,CAAG,EACtDI,GAAaI,EACbnB,EAAS,OAAO,UAAYmB,EAExBA,EAAcP,GAAQ,KAC5B,CAEA,OAAAxC,EAAK,UAAU0C,EAAUC,EAAW,EAAI,EAC5B,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,UACEK,EACAC,EACArE,EACAsE,EACAC,EACAC,EACAC,EACAC,EACAC,EACA,CACA,IAAI3B,EAAW,KAAK,IAAI,IAAIoB,CAAK,EACjC,GAAI,CAACpB,EAAU,MAAY,GAG3B,IAAM4B,EADM,IAAI,WAAW,KAAK,KAAK,OAAO,MAAM,EAC3B,MAAM5E,EAAMA,EAAOsE,CAAO,EAC3CO,EAAa,KAAK,YAAY,OAAOD,CAAU,EACjDE,EAAeD,EAEnB,IAAI1E,EAAK,EACT,IAAMiB,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAG7C4B,EAAS,cACP6B,EAAW,WAAW,GAAG,IAC3BC,EAAeD,EAAW,MAAM,CAAC,GAEnCC,EACE9B,EAAS,aACRA,EAAS,YAAY,SAAS,GAAG,EAAI,GAAK,KAC3C8B,GAGJ,IAAIC,EAAS,GACT9B,EAAQ,KACZ,IAAM+B,IAAYT,EAAc,IAAsB,EAChDU,IACHV,EAAc,IAA0B,EACrCW,GAAeX,EAAc,IAAqB,EAClDY,GAAcZ,EAAc,IAAsB,EACxD,GAAI,CAEFtB,EAAQ,KAAK,GAAG,SAAS6B,CAAY,EACrCC,EAAS,EACX,MAAY,CAAC,CAEb,IAAIG,GAAeC,IACbD,GAAeH,EAEjB,OAAA3D,EAAK,UAAUuD,EAAOxE,EAAI,EAAI,EAClB,GAMhB,IAAMiF,EAAa,CAAE,KAAMN,EAAc,SAAU,CAAE,EAC/CrE,EAAO,OACbN,EAAK,KAAK,SACV,IAAMkF,EAAa,CAAE,KAAA5E,EAAM,OAAQ2E,EAAY,GAAAjF,CAAG,EAClD,YAAK,IAAI,IAAIA,EAAIkF,CAAU,EAE3BrC,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAG1B6C,EAAS,OAAO,SAAW,EAEvBmC,IAEFnC,EAAS,OAAO,KAAO,GAWzB5B,EAAK,UAAUuD,EAAOxE,EAAI,EAAI,EAClB,CACd,CAEA,UAAUL,EAAM,CACd,MAAM,IAAID,EAAaC,CAAI,CAC7B,CAEA,cAAcK,EAAImF,EAAS,CACzB,IAAMtC,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAE3B,IAAM5B,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAG7CmE,EACJ,OAAQvC,EAAS,KAAM,CACrB,IAAK,QACHuC,EAAgB,EAChB,MACF,IAAK,YACHA,EAAgB,EAChB,MACF,IAAK,OACHA,EAAgB,EAChB,MACF,QACEA,EAAgB,CACpB,CACAnE,EAAK,SAASkE,EAASC,CAAQ,EAI/B,IAAIb,EAAU,EACV1B,EAAS,SAAQ0B,GAAgB,GACrCtD,EAAK,UAAUkE,EAAU,EAAGZ,EAAS,EAAI,EAIzC,IAAIF,EAAe,GACfxB,EAAS,OAAS,OACpBwB,EACE,QAKOxB,EAAS,OAAS,cAC3BwB,EACE,WAMJ,IAAMgB,EAAK,OAAOhB,CAAY,EAC9B,OAAApD,EAAK,aAAakE,EAAU,EAAGE,EAAI,EAAI,EAIvCpE,EAAK,aAAakE,EAAU,GAAIE,EAAI,EAAI,EAE5B,CACd,CAEA,oBAAoBrF,EAAII,EAAO,CAC7B,IAAMyC,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAG3B,IAAMyC,EACJ,GAMF,GAAIlF,EAAQ,CAACkF,EACX,MAAY,IAId,GAAIzC,EAAS,OAAS,QACpB,MAAY,IAGd,GAAI,CAEF,OAAAA,EAAS,OAAS,GAAQzC,EAAa,GAInCyC,EAAS,QAAU,OAAO,KAAK,GAAG,cAAiB,YACrD,KAAK,GAAG,aAAaA,EAAS,OAAQzC,CAAK,EAGjC,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,eAAeJ,EAAIuF,EAAY,CAC7B,IAAM1C,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAG3B,GAAIA,EAAS,OAAS,YACpB,MAAY,GAId,GAAI,CAACA,EAAS,YACZ,MAAY,GAGd,IAAM5B,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EASjDA,EAAK,SAASsE,EAAiB,CAAe,EAG9C,IAAMC,EAAa3C,EAAS,YAAY,OACxC,OAAA5B,EAAK,UAAUsE,EAAa,EAAGC,EAAY,EAAI,EAEnC,CACd,CAEA,oBAAoBxF,EAAIyF,EAAStB,EAAS,CACxC,IAAMtB,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAG3B,GAAIA,EAAS,OAAS,YACpB,MAAY,GAId,GAAI,CAACA,EAAS,YACZ,MAAY,GAId,GAAIsB,EAAUtB,EAAS,YAAY,OACjC,MAAY,IAId,IAAM3B,EAAM,IAAI,WAAW,KAAK,KAAK,OAAO,MAAM,EAC5CwE,EAAY,KAAK,YAAY,OAAO7C,EAAS,WAAW,EAC9D,OAAA3B,EAAI,IAAIwE,EAAWD,CAAO,EAEd,CACd,CAEA,kBAAkBzF,EAAII,EAAOqF,EAAStB,EAASwB,EAAa,CAC1D,IAAM9C,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAG3B,IAAM3B,EAAM,IAAI,WAAW,KAAK,KAAK,OAAO,MAAM,EAC5CwE,EAAY,IAAI,WAAWxE,EAAI,OAAQuE,EAAStB,CAAO,EACvDO,EAAa,KAAK,YAAY,OAAOgB,CAAS,EAEpD,GAAI,CAEF,IAAIf,EAAeD,EACf7B,EAAS,cAEP6B,EAAW,WAAW,GAAG,IAC3BC,EAAeD,EAAW,MAAM,CAAC,GAGnCC,EACE9B,EAAS,aACRA,EAAS,YAAY,SAAS,GAAG,EAAI,GAAK,KAC3C8B,GAIJ,IAAM7B,EAAQ,KAAK,GAAG,SAAS6B,EAAc,CAC3C,gBAAiBvE,EAAa,UAAgC,CAChE,CAAC,EAEKa,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAejDA,EAAK,aAAa0E,EAAa,OAAO7C,EAAM,KAAO,CAAC,EAAG,EAAI,EAG3D7B,EAAK,aAAa0E,EAAc,EAAG,OAAO7C,EAAM,KAAO,CAAC,EAAG,EAAI,EAG/D,IAAIsC,EAAgB,EACpB,OAAItC,EAAM,OAAO,EAAGsC,EAAgB,EAC3BtC,EAAM,YAAY,EAAGsC,EAAgB,EACrCtC,EAAM,eAAe,EAAGsC,EAAgB,EACxCtC,EAAM,kBAAkB,EAC/BsC,EAAgB,EACTtC,EAAM,cAAc,EAAGsC,EAAgB,EACvCtC,EAAM,OAAO,IAAGsC,EAAgB,GACzCnE,EAAK,SAAS0E,EAAc,GAAIP,CAAQ,EAGxCnE,EAAK,aAAa0E,EAAc,GAAI,OAAO7C,EAAM,OAAS,CAAC,EAAG,EAAI,EAGlE7B,EAAK,aAAa0E,EAAc,GAAI,OAAO7C,EAAM,MAAQ,CAAC,EAAG,EAAI,EAGjE7B,EAAK,aACH0E,EAAc,GACd,OAAO7C,EAAM,QAAU,GAAS,EAChC,EACF,EAGA7B,EAAK,aACH0E,EAAc,GACd,OAAO7C,EAAM,QAAU,GAAS,EAChC,EACF,EAGA7B,EAAK,aACH0E,EAAc,GACd,OAAO7C,EAAM,QAAU,GAAS,EAChC,EACF,EAEY,CACd,OAASlC,EAAG,CACV,OAAIA,EAAE,OAAS,SAAsB,GACjCA,EAAE,OAAS,SAAsB,EACzB,EACd,CACF,CAGA,UAAUZ,EAAI0C,EAAQnC,EAAKqF,EAAQ,CACjC,IAAM/C,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,OAAK6C,EACDA,EAAS,OAAS,OAAoB,EAI9B,EALe,CAM7B,CAEA,YAAY7C,EAAI0C,EAAQnC,EAAK,CAC3B,IAAMsC,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAC3B,GAAIA,EAAS,OAAS,OAAQ,MAAY,GAE1C,GAAI,CAEF,IAAMC,EAAQ,KAAK,GAAG,SAASD,EAAS,OAAO,IAAI,EAC7CgD,EAAU,OAAOnD,CAAM,EAAI,OAAOnC,CAAG,EAC3C,GAAIsF,EAAU/C,EAAM,KAAM,CAExB,IAAMgD,EAAQ,IAAI,WAAWD,EAAU/C,EAAM,IAAI,EACjD,KAAK,GAAG,eAAeD,EAAS,OAAO,KAAMiD,CAAK,CACpD,CACA,MAAY,EACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,YAAY9F,EAAI,CACd,IAAM6C,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAC3B,GAAIA,EAAS,OAAS,OAAQ,MAAY,GAE1C,GAAI,CAGF,OAAI,OAAO,KAAK,GAAG,WAAc,YAC/B,KAAK,GAAG,UAAUA,EAAS,OAAO,IAAI,EAE5B,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,gBAAgB7C,EAAIuD,EAAK,CACvB,IAAMV,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAC3B,GAAI,CAACA,EAAS,OAAQ,MAAY,GAClC,IAAM3B,EAAM,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC1C4B,EAAQ,KAAK,GAAG,SAASD,EAAS,OAAO,IAAI,EACnD,OAAA3B,EAAI,aAAaqC,EAAK,OAAOT,EAAM,GAAG,EAAG,EAAI,EAC7C5B,EAAI,aAAaqC,EAAM,EAAG,OAAOT,EAAM,GAAG,EAAG,EAAI,EACjD5B,EAAI,SAASqC,EAAM,GAAIT,EAAM,QAAQ,EACrC5B,EAAI,aAAaqC,EAAM,GAAI,OAAOT,EAAM,KAAK,EAAG,EAAI,EACpD5B,EAAI,aAAaqC,EAAM,GAAI,OAAOT,EAAM,IAAI,EAAG,EAAI,EACnD5B,EAAI,aAAaqC,EAAM,GAAI,OAAOT,EAAM,KAAK,EAAG,EAAI,EACpD5B,EAAI,aAAaqC,EAAM,GAAI,OAAOT,EAAM,KAAK,EAAG,EAAI,EACpD5B,EAAI,aAAaqC,EAAM,GAAI,OAAOT,EAAM,KAAK,EAAG,EAAI,EACxC,CACd,CAEA,qBAAqB9C,EAAI+F,EAAM,CAC7B,IAAMlD,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAC3B,GAAIA,EAAS,OAAS,OAAQ,MAAY,GAE1C,GAAI,CACF,YAAK,GAAG,aAAaA,EAAS,OAAO,KAAM,OAAOkD,CAAI,CAAC,EAC3C,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,sBAAsB/F,EAAIgG,EAAMC,EAAMC,EAAW,CAC/C,IAAMrD,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAC3B,GAAIA,EAAS,OAAS,OAAQ,MAAY,GAE1C,GAAI,CACF,IAAMsD,EAAQ,CACZ,MAAO,OAAOH,CAAI,EAAI,IACtB,MAAO,OAAOC,CAAI,EAAI,GACxB,EAEA,YAAK,GAAG,WAAWpD,EAAS,OAAO,KAAMsD,EAAM,MAAOA,EAAM,KAAK,EACrD,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,SAASnG,EAAIiD,EAAMC,EAASR,EAAQiB,EAAU,CAC5C,IAAMd,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAC3B,GAAIA,EAAS,OAAS,OAAQ,MAAY,GAE1C,GAAI,CACF,IAAMgB,EAAU,KAAK,GAAG,aAAahB,EAAS,OAAO,IAAI,EACrDe,EAAY,EACV3C,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC3CC,EAAM,IAAI,WAAW,KAAK,KAAK,OAAO,MAAM,EAE5CkF,EAAW,OAAO1D,CAAM,EAE9B,QAASY,EAAI,EAAGA,EAAIJ,EAASI,IAAK,CAChC,IAAMC,EAAMN,EAAOK,EAAI,EACjBE,EAAMvC,EAAK,UAAUsC,EAAK,EAAI,EAC9BE,EAASxC,EAAK,UAAUsC,EAAM,EAAG,EAAI,EAErCO,EAAQsC,EAAWxC,EACnBG,EAAM,KAAK,IAAID,EAAQL,EAAQI,EAAQ,MAAM,EAC7CG,EAAcD,EAAMD,EAO1B,GALIE,GAAe,IAEnB9C,EAAI,IAAI,IAAI,WAAW2C,EAAQ,MAAMC,EAAOC,CAAG,CAAC,EAAGP,CAAG,EACtDI,GAAaI,EAETA,EAAcP,GAAQ,KAC5B,CAEA,OAAAxC,EAAK,UAAU0C,EAAUC,EAAW,EAAI,EAC5B,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,UAAU5D,EAAIiD,EAAMC,EAASR,EAAQS,EAAa,CAChD,IAAMN,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAC3B,GAAIA,EAAS,OAAS,OAAQ,MAAY,GAE1C,GAAI,CACF,IAAIO,EAAU,EACRC,EAAS,CAAC,EACVpC,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC3CC,EAAM,IAAI,WAAW,KAAK,KAAK,OAAO,MAAM,EAElD,QAASoC,EAAI,EAAGA,EAAIJ,EAASI,IAAK,CAChC,IAAMC,EAAMN,EAAOK,EAAI,EACjBE,EAAMvC,EAAK,UAAUsC,EAAK,EAAI,EAC9BE,EAASxC,EAAK,UAAUsC,EAAM,EAAG,EAAI,EAC3CF,EAAO,KAAK,IAAI,WAAWnC,EAAI,OAAQsC,EAAKC,CAAM,CAAC,EACnDL,GAAWK,CACb,CAEA,IAAI5C,EACJ,GAAIwC,EAAO,SAAW,EACpBxC,EAASwC,EAAO,CAAC,MACZ,CACLxC,EAAS,IAAI,WAAWuC,CAAO,EAC/B,IAAIV,EAAS,EACb,QAAWgB,KAASL,EAClBxC,EAAO,IAAI6C,EAAOhB,CAAM,EACxBA,GAAUgB,EAAM,MAEpB,CAGA,IAAMG,EAAU,KAAK,GAAG,aAAahB,EAAS,OAAO,IAAI,EACnDwD,EAAa,IAAI,WACrB,KAAK,IAAI,OAAO3D,CAAM,EAAI7B,EAAO,OAAQgD,EAAQ,MAAM,CACzD,EAGA,OAAAwC,EAAW,IAAIxC,CAAO,EAEtBwC,EAAW,IAAIxF,EAAQ,OAAO6B,CAAM,CAAC,EAGrC,KAAK,GAAG,cAAcG,EAAS,OAAO,KAAMwD,CAAU,EAEtDpF,EAAK,UAAUkC,EAAaC,EAAS,EAAI,EAC7B,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,WAAWpD,EAAIwD,EAAKC,EAAQ6C,EAAQC,EAAY,CAC9C,IAAM1D,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAC3B,GAAIA,EAAS,OAAS,YAAa,MAAY,IAE/C,GAAI,CACF,IAAM2D,EAAU,KAAK,GAAG,YAAY3D,EAAS,OAAO,KAAM,CACxD,cAAe,EACjB,CAAC,EACK5B,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC3CC,EAAM,IAAI,WAAW,KAAK,KAAK,OAAO,MAAM,EAE9CwB,EAAS,EACT+D,EAAiB,EAGfC,EAAa,OAAOJ,CAAM,EAEhC,QAAShD,EAAIoD,EAAYpD,EAAIkD,EAAQ,OAAQlD,IAAK,CAChD,IAAMzB,EAAQ2E,EAAQlD,CAAC,EACjBqD,EAAO9E,EAAM,KACb+E,EAAY,KAAK,YAAY,OAAOD,CAAI,EAGxCE,EAAa,GAAKD,EAAU,OAElC,GAAIlE,EAASmE,EAAapD,EACxB,MAIFxC,EAAK,aAAauC,EAAMd,EAAQ,OAAOY,EAAI,CAAC,EAAG,EAAI,EACnDrC,EAAK,aAAauC,EAAMd,EAAS,EAAG,GAAI,EAAI,EAC5CzB,EAAK,UAAUuC,EAAMd,EAAS,GAAIkE,EAAU,OAAQ,EAAI,EAGxD,IAAIxB,EAAgB,EAChBvD,EAAM,OAAO,EAAGuD,EAAgB,EAC3BvD,EAAM,YAAY,IAAGuD,EAAgB,GAC9CnE,EAAK,SAASuC,EAAMd,EAAS,GAAI0C,CAAQ,EAGzClE,EAAI,IAAI0F,EAAWpD,EAAMd,EAAS,EAAE,EAEpCA,GAAUmE,EACVJ,GACF,CAEA,OAAAxF,EAAK,UAAUsF,EAAY7D,EAAQ,EAAI,EAC3B,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,YAAYoE,EAAMC,EAAI,CACpB,IAAMC,EAAW,KAAK,IAAI,IAAIF,CAAI,EAClC,OAAKE,GAGL,KAAK,IAAI,OAAOD,CAAE,EAGlB,KAAK,IAAI,IAAIA,EAAIC,CAAQ,EACzB,KAAK,IAAI,OAAOF,CAAI,EAER,GATe,CAU7B,CAEA,QAAQ9G,EAAI,CACV,IAAM6C,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAC3B,GAAIA,EAAS,OAAS,OAAQ,MAAY,GAE1C,GAAI,CAEF,OAAI,OAAO,KAAK,GAAG,WAAc,YAC/B,KAAK,GAAG,UAAUA,EAAS,OAAO,IAAI,EAE5B,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,QAAQ7C,EAAIiH,EAAW,CACrB,IAAMpE,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,OAAK6C,EACDA,EAAS,OAAS,OAAoB,GAE7B,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC5C,aAAaoE,EAAW,OAAOpE,EAAS,OAAO,QAAQ,EAAG,EAAI,EACvD,GALe,CAM7B,CAGA,sBAAsB7C,EAAIH,EAAMsE,EAAS,CACvC,IAAMtB,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAE3B,IAAM6B,EAAa,KAAK,YAAY,OAClC,IAAI,WAAW,KAAK,KAAK,OAAO,OAAQ7E,EAAMsE,CAAO,CACvD,EAEA,GAAI,CACF,IAAIQ,EAAeD,EACnB,OAAI7B,EAAS,cACP6B,EAAW,WAAW,GAAG,IAC3BC,EAAeD,EAAW,MAAM,CAAC,GAEnCC,EAAe9B,EAAS,YAAc,IAAM8B,GAG9C,KAAK,GAAG,UAAUA,CAAY,EAClB,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,wBAAwB3E,EAAII,EAAOP,EAAMsE,EAAS6B,EAAMC,EAAMC,EAAW,CACvE,IAAMrD,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAE3B,IAAM6B,EAAa,KAAK,YAAY,OAClC,IAAI,WAAW,KAAK,KAAK,OAAO,OAAQ7E,EAAMsE,CAAO,CACvD,EAEA,GAAI,CACF,IAAIQ,EAAeD,EACf7B,EAAS,cACP6B,EAAW,WAAW,GAAG,IAC3BC,EAAeD,EAAW,MAAM,CAAC,GAEnCC,EAAe9B,EAAS,YAAc,IAAM8B,GAG9C,IAAMwB,EAAQ,CACZ,MAAO,OAAOH,CAAI,EAAI,IACtB,MAAO,OAAOC,CAAI,EAAI,GACxB,EAEA,YAAK,GAAG,WAAWtB,EAAcwB,EAAM,MAAOA,EAAM,KAAK,EAC7C,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,UACEe,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACA,CACA,IAAMC,EAAc,KAAK,IAAI,IAAIP,CAAM,EACjCQ,EAAc,KAAK,IAAI,IAAIJ,CAAM,EACvC,GAAI,CAACG,GAAe,CAACC,EAAa,MAAY,GAE9C,IAAMC,EAAgB,KAAK,YAAY,OACrC,IAAI,WAAW,KAAK,KAAK,OAAO,OAAQP,EAAUC,CAAY,CAChE,EACMO,EAAgB,KAAK,YAAY,OACrC,IAAI,WAAW,KAAK,KAAK,OAAO,OAAQL,EAAUC,CAAY,CAChE,EAEA,GAAI,CACF,IAAIK,EAAkBF,EAClBG,EAAkBF,EAEtB,OAAIH,EAAY,cACVE,EAAc,WAAW,GAAG,IAC9BE,EAAkBF,EAAc,MAAM,CAAC,GAEzCE,EAAkBJ,EAAY,YAAc,IAAMI,GAGhDH,EAAY,cACVE,EAAc,WAAW,GAAG,IAC9BE,EAAkBF,EAAc,MAAM,CAAC,GAEzCE,EAAkBJ,EAAY,YAAc,IAAMI,GAGpD,KAAK,GAAG,SAASD,EAAiBC,CAAe,EACrC,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,cAAc9H,EAAIH,EAAMkI,EAAUvE,EAAKwE,EAASC,EAAS,CACvD,IAAMpF,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAE3B,IAAM6B,EAAa,KAAK,YAAY,OAClC,IAAI,WAAW,KAAK,KAAK,OAAO,OAAQ7E,EAAMkI,CAAQ,CACxD,EAEA,GAAI,CACF,IAAIpD,EAAeD,EACf7B,EAAS,cACP6B,EAAW,WAAW,GAAG,IAC3BC,EAAeD,EAAW,MAAM,CAAC,GAEnCC,EAAe9B,EAAS,YAAc,IAAM8B,GAG9C,IAAMuD,EAAa,KAAK,GAAG,aAAavD,CAAY,EAC9CwD,EAAY,KAAK,YAAY,OAAOD,CAAU,EAEpD,OAAIC,EAAU,OAASH,EACT,IAGF,IAAI,WAAW,KAAK,KAAK,OAAO,MAAM,EAC9C,IAAIG,EAAW3E,CAAG,EAET,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC5C,UAAUyE,EAASE,EAAU,OAAQ,EAAI,EAElC,EACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,sBAAsBnI,EAAIH,EAAMkI,EAAU,CACxC,IAAMlF,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAE3B,IAAM6B,EAAa,KAAK,YAAY,OAClC,IAAI,WAAW,KAAK,KAAK,OAAO,OAAQ7E,EAAMkI,CAAQ,CACxD,EAEA,GAAI,CACF,IAAIpD,EAAeD,EACnB,OAAI7B,EAAS,cACP6B,EAAW,WAAW,GAAG,IAC3BC,EAAeD,EAAW,MAAM,CAAC,GAEnCC,EAAe9B,EAAS,YAAc,IAAM8B,GAG9C,KAAK,GAAG,UAAUA,CAAY,EAClB,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,YAAYuC,EAAQE,EAAUC,EAAcC,EAAQC,EAAUC,EAAc,CAC1E,IAAMC,EAAc,KAAK,IAAI,IAAIP,CAAM,EACjCQ,EAAc,KAAK,IAAI,IAAIJ,CAAM,EACvC,GAAI,CAACG,GAAe,CAACC,EAAa,MAAY,GAE9C,IAAMC,EAAgB,KAAK,YAAY,OACrC,IAAI,WAAW,KAAK,KAAK,OAAO,OAAQP,EAAUC,CAAY,CAChE,EACMO,EAAgB,KAAK,YAAY,OACrC,IAAI,WAAW,KAAK,KAAK,OAAO,OAAQL,EAAUC,CAAY,CAChE,EAEA,GAAI,CACF,IAAIK,EAAkBF,EAClBG,EAAkBF,EAEtB,OAAIH,EAAY,cACVE,EAAc,WAAW,GAAG,IAC9BE,EAAkBF,EAAc,MAAM,CAAC,GAEzCE,EAAkBJ,EAAY,YAAc,IAAMI,GAGhDH,EAAY,cACVE,EAAc,WAAW,GAAG,IAC9BE,EAAkBF,EAAc,MAAM,CAAC,GAEzCE,EAAkBJ,EAAY,YAAc,IAAMI,GAGpD,KAAK,GAAG,WAAWD,EAAiBC,CAAe,EACvC,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,aAAaV,EAAUC,EAAcrH,EAAIuH,EAAUC,EAAc,CAC/D,IAAM3E,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAE3B,IAAM8E,EAAgB,KAAK,YAAY,OACrC,IAAI,WAAW,KAAK,KAAK,OAAO,OAAQP,EAAUC,CAAY,CAChE,EACMO,EAAgB,KAAK,YAAY,OACrC,IAAI,WAAW,KAAK,KAAK,OAAO,OAAQL,EAAUC,CAAY,CAChE,EAEA,GAAI,CACF,IAAIM,EAAkBF,EACtB,OAAI/E,EAAS,cACP+E,EAAc,WAAW,GAAG,IAC9BE,EAAkBF,EAAc,MAAM,CAAC,GAEzCE,EAAkBjF,EAAS,YAAc,IAAMiF,GAGjD,KAAK,GAAG,YAAYH,EAAeG,CAAe,EACtC,CACd,MAAY,CACV,MAAY,GACd,CACF,CAEA,iBAAiB9H,EAAIH,EAAMkI,EAAU,CACnC,IAAMlF,EAAW,KAAK,IAAI,IAAI7C,CAAE,EAChC,GAAI,CAAC6C,EAAU,MAAY,GAE3B,IAAM6B,EAAa,KAAK,YAAY,OAClC,IAAI,WAAW,KAAK,KAAK,OAAO,OAAQ7E,EAAMkI,CAAQ,CACxD,EAEA,GAAI,CACF,IAAIpD,EAAeD,EACnB,OAAI7B,EAAS,cACP6B,EAAW,WAAW,GAAG,IAC3BC,EAAeD,EAAW,MAAM,CAAC,GAEnCC,EAAe9B,EAAS,YAAc,IAAM8B,GAG9C,KAAK,GAAG,WAAWA,CAAY,EACnB,CACd,MAAY,CACV,MAAY,GACd,CACF,CAGA,YAAYyD,EAAKC,EAAKC,EAAgBC,EAAS,CAE7C,IAAMtH,EAAO,IAAI,SAAS,KAAK,KAAK,OAAO,MAAM,EAC7CuH,EAAY,EAEhB,QAASlF,EAAI,EAAGA,EAAIgF,EAAgBhF,IAAK,CACvC,IAAMmF,EAASL,EAAM9E,EAAI,GACnBoF,EAAWzH,EAAK,aAAawH,EAAQ,EAAI,EACzCnI,EAAOW,EAAK,SAASwH,EAAS,CAAC,EAG/BE,EAAWN,EAAMG,EAAY,GACnCvH,EAAK,aAAa0H,EAAUD,EAAU,EAAI,EAC1CzH,EAAK,SAAS0H,EAAW,EAAGrI,CAAI,EAChCW,EAAK,SAAS0H,EAAW,EAAQ,CAAgC,EACjE1H,EAAK,UAAU0H,EAAW,GAAI,EAAG,EAAI,EAErCH,GACF,CAEA,OAAAvH,EAAK,UAAUsH,EAASC,EAAW,EAAI,EAC3B,CACd,CAGA,WAAWhF,EAAKwE,EAAS,CACvB,IAAMY,EAAQ,IAAI,WAAW,KAAK,KAAK,OAAO,OAAQpF,EAAKwE,CAAO,EAClE,cAAO,gBAAgBY,CAAK,EAChB,CACd,CAGA,aAAc,CACZ,UAAG,YAAY,EACH,CACd,CAGA,YAAY5I,EAAII,EAAO,CACrB,MAAY,GACd,CAEA,UAAUJ,EAAI6I,EAAQC,EAAS,CAC7B,MAAY,GACd,CAEA,UAAU9I,EAAI+I,EAAQD,EAAS,CAC7B,MAAY,GACd,CAEA,cAAc9I,EAAIgJ,EAAK,CACrB,MAAY,GACd,CACF,ECrzCA,IAAMC,GACJ,OAAO,SAAY,UACnB,OAAO,QAAQ,UAAa,UAC5B,OAAO,QAAQ,SAAS,MAAS,SAMnC,SAASC,GAAqBC,EAAQC,EAA8B,CAmBlE,MAAO,CAEL,GAAGD,EAEH,GAAG,OAAO,YAtBY,CACtB,iBACA,YACA,WACA,eACA,YACA,cACA,eACA,eACA,aACA,YACA,WACA,cACA,eACA,aACA,aACA,eACF,EAOO,IAAKE,GAAW,CACf,IAAMC,EAASD,EAAO,MAAM,EAAGA,EAAO,OAAS,CAAC,EAChD,OAAMC,KAAUH,EAQT,CACLE,EACA,IAAIE,KACEF,IAAW,iBAAmBE,EAAK,CAAC,IAAM,gBAC5CH,EAAI,KAAKG,EAAK,CAAC,CAAC,EAEVJ,EAAWG,CAAM,EAAE,GAAGC,CAAI,EAEtC,EAfS,CACLF,EACA,IAAM,CACJ,MAAM,IAAI,MAAM,GAAGC,CAAM,mBAAmB,CAC9C,CACF,CAWJ,CAAC,EACA,OACEE,GAA8CA,IAAU,MAC3D,CACJ,CACF,CACF,CAKA,eAAeC,GAAW,CACxB,GAAAC,EACA,KAAAH,CACF,EAG4C,CAC1C,IAAMI,EAAM,IAAI,IAAI,iBAAkB,YAAY,GAAG,EAC/CP,EAAoB,CAAC,EACrBQ,EAAKV,GAAqBQ,EAAG,OAAO,GAAIN,CAAG,EAE3CS,EAAO,IAAIC,EAAa,CAC5B,GAAIF,EACJ,KAAM,CAAC,UAAW,GAAGL,CAAI,EACzB,IAAK,CACH,IAAK,GACP,CACF,CAAC,EAEDM,EAAK,OAAUE,GAAY,CAE3B,EACA,IAAMC,EAAc,IAAI,YACpBC,EAAe,GAEnBJ,EAAK,OAAUE,GAAY,CACzB,IAAMG,EAAOF,EAAY,OAAOD,CAAO,EACnCG,IAAMD,GAAgBC,EAC5B,EACAL,EAAK,YAAc,IAAM,CACvB,IAAMM,EAAO,oCACPC,EAAQ,qCACd,GAAIR,EAAG,YAAYO,CAAI,EAAE,OAAQ,CAE/B,IAAME,EAAQT,EAAG,aAAaO,CAAI,EAMlC,GAHAP,EAAG,WAAWO,CAAI,EAGdE,EAAM,CAAC,IAAM,EAAG,CAClB,IAAMC,EAAQ,IAAI,WAAW,CAC3B,GAAGC,GACH,GAAGC,GACH,GAAGC,EACL,CAAC,EACD,OAAAb,EAAG,cAAcQ,EAAOE,CAAK,EACtB,CACT,CAGA,IAAMA,EAAQZ,EAAG,oBAAoBW,CAAK,EAC1CT,EAAG,cAAcQ,EAAOE,CAAK,CAC/B,CACA,MAAO,EACT,EAIA,MAAMV,EAAG,UAAU,WAAY,KAAM,CAAE,KAAM,EAAG,CAAC,EAEjD,IAAIc,EAEJ,GAAIzB,GAAS,CAEX,IAAM0B,EAAO,MADF,KAAM,QAAO,aAAa,GACf,SAAShB,EAAI,SAAS,EAAE,MAAM,CAAC,CAAC,EACtDe,EAAM,MAAM,YAAY,YAAYC,EAAM,CACxC,uBAAwBd,CAC1B,CAAC,CACH,MACEa,EAAM,MAAM,YAAY,qBAAqB,MAAMf,CAAG,EAAG,CACvD,uBAAwBE,CAC1B,CAAC,EAGH,IAAIe,EACJ,aAAMlB,EAAG,aAAa,SAAY,CAChCkB,EAAWf,EAAK,MAAMa,EAAI,SAAS,OAAO,CAC5C,CAAC,EACM,CAACE,EAAWxB,EAAKa,CAAY,CACtC,CAWA,eAAsBY,GAAO,CAC3B,GAAAnB,EACA,KAAAH,EACA,SAAAuB,EAAW,UACb,EAAkB,CAIhB,IAAMC,GAHgB,MAAMrB,EAAG,MAC7B,mBACF,GACkC,KAAK,CAAC,EAAE,YAEpCsB,EAAU,eACVC,EAAW,CACf,KACA,WACA,YACA,KACA,IACA,KACAD,EACA,UACF,EAEM,CAACJ,EAAUxB,EAAKa,CAAY,EAAI,MAAMR,GAAW,CACrD,GAAAC,EACA,KAAM,CAAC,GAAIH,GAAQ,CAAC,EAAI,GAAG0B,CAAQ,CACrC,CAAC,EAID,GAFAvB,EAAG,KAAK,qCAAqCqB,CAAW,EAAE,EAEtDH,IAAa,EACf,MAAM,IAAI,MACR,iCAAiCA,CAAQ;AAAA,iBAAsBX,CAAY,EAC7E,EAGF,IAAMiB,EAAO,IAAI,KAAK9B,EAAK0B,EAAU,CACnC,KAAM,YACR,CAAC,EACD,OAAApB,EAAG,OAAO,GAAG,OAAOsB,CAAO,EAEpBE,CACT,CAIA,SAASC,EAAWC,EAAc,CAChC,OAAOA,EAAK,WAAW,CAAC,CAC1B,CAGA,SAASC,EAAaC,EAAe,CACnC,IAAMC,EAAS,IAAI,YAAY,CAAC,EAEhC,OADa,IAAI,SAASA,CAAM,EAC3B,SAAS,EAAGD,EAAO,EAAK,EACtB,IAAI,WAAWC,CAAM,CAC9B,CAGA,SAASC,EAAcC,EAAa,CAElC,IAAMC,EADc,IAAI,YAAY,EACP,OAAOD,CAAG,EACvC,OAAO,IAAI,WAAW,CAAC,GAAGC,EAAU,CAAC,CAAC,CACxC,CAEA,IAAMnB,GAAS,IAAI,WAAW,CAC5BY,EAAW,GAAG,EACd,GAAGE,EAAa,CAAC,EACjB,GAAGA,EAAa,CAAC,CACnB,CAAC,EACKZ,GAAgB,IAAI,WAAW,CACnCU,EAAW,GAAG,EACd,GAAGE,EAAa,CAAC,EACjBF,EAAW,GAAG,CAChB,CAAC,EAEKQ,EAAcH,EAAc,gBAAgB,EAC5CI,EAAeJ,EAAc,qBAAqB,EAClDK,GAAgB,EAAIF,EAAY,OAASC,EAAa,OACtDpB,GAAe,IAAI,WAAW,CAClCW,EAAW,GAAG,EACd,GAAGE,EAAaQ,EAAa,EAC7B,GAAGF,EACH,GAAGC,CACL,CAAC", "names": ["WASIProcExit", "code", "FSDummy", "path", "data", "options", "fd", "existingPath", "newPath", "old<PERSON><PERSON>", "flags", "target", "type", "len", "atime", "mtime", "WasiPreview1", "wasm", "e", "buffer", "text", "argvP", "argvBufP", "view", "mem", "arg", "encoded", "argcPtr", "argvBufSizePtr", "bufSize", "acc", "environP", "environBufP", "key", "value", "entry", "environCountPtr", "environBufSizePtr", "count", "k", "v", "id", "resPtr", "resolution", "precision", "timePtr", "time", "ms", "offset", "whence", "newOffsetPtr", "fileDesc", "stats", "newPosition", "noffset", "iovs", "iovsLen", "nwrittenPtr", "written", "chunks", "i", "ptr", "buf", "bufLen", "chunk", "nreadPtr", "totalRead", "content", "start", "end", "bytesToRead", "dirfd", "<PERSON><PERSON><PERSON>s", "pathLen", "oflags", "fsRightsBase", "fsRightsInheriting", "fdflags", "fdPtr", "pathB<PERSON>er", "pathString", "<PERSON><PERSON><PERSON>", "exists", "o_create", "o_directory", "o_exclusive", "o_truncate", "fileHandle", "descriptor", "statPtr", "filetype", "bf", "validFlags", "prestatPtr", "<PERSON><PERSON><PERSON><PERSON>", "pathPtr", "pathBytes", "filestatPtr", "advice", "newSize", "zeros", "size", "atim", "mtim", "fst_flags", "times", "position", "newContent", "cookie", "bufusedPtr", "entries", "<PERSON><PERSON><PERSON><PERSON>", "startIndex", "name", "nameBytes", "direntSize", "from", "to", "fromDesc", "offsetPtr", "old_fd", "old_flags", "old_path", "old_path_len", "new_fd", "new_path", "new_path_len", "oldFileDesc", "newFileDesc", "oldPathString", "newPathString", "resolved<PERSON>ld<PERSON>ath", "resolved<PERSON>ew<PERSON><PERSON>", "path_len", "buf_len", "bufused", "linkString", "linkBytes", "in_", "out", "nsubscriptions", "nevents", "numEvents", "subPtr", "userdata", "eventPtr", "bytes", "riData", "riFlags", "siData", "how", "IN_NODE", "emscriptenFsToWasiFS", "fs", "acc", "method", "target", "args", "entry", "execPgDump", "pg", "bin", "FS", "wasi", "WasiPreview1", "_buffer", "textDecoder", "errorMessage", "text", "pgIn", "pgOut", "msgIn", "reply", "authOk", "versionParam", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "app", "blob", "exitCode", "pgDump", "fileName", "search_path", "outFile", "baseArgs", "file", "charToByte", "char", "int32ToBytes", "value", "buffer", "stringToBytes", "str", "strBytes", "svParamName", "svParamValue", "svTotalLength"]}