{"name": "@electric-sql/pglite", "version": "0.2.17", "private": false, "publishConfig": {"access": "public"}, "description": "PGlite is a WASM Postgres build packaged into a TypeScript client library that enables you to run Postgres in the browser, Node.js and Bun, with no need to install any other dependencies. It is only 3.7mb gzipped.", "keywords": ["postgres", "sql", "database", "wasm", "client", "pglite"], "author": "Electric DB Limited", "homepage": "https://pglite.dev", "license": "Apache-2.0", "main": "dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./template": {"types": "./dist/templating.d.ts", "import": "./dist/templating.js", "require": "./dist/templating.cjs"}, "./live": {"types": "./dist/live/index.d.ts", "import": "./dist/live/index.js", "require": "./dist/live/index.cjs"}, "./worker": {"types": "./dist/worker/index.d.ts", "import": "./dist/worker/index.js", "require": "./dist/worker/index.cjs"}, "./vector": {"types": "./dist/vector/index.d.ts", "import": "./dist/vector/index.js", "require": "./dist/vector/index.cjs"}, "./nodefs": {"types": "./dist/fs/nodefs.d.ts", "import": "./dist/fs/nodefs.js", "require": "./dist/fs/nodefs.cjs"}, "./opfs-ahp": {"types": "./dist/fs/opfs-ahp.d.ts", "import": "./dist/fs/opfs-ahp.js", "require": "./dist/fs/opfs-ahp.cjs"}, "./basefs": {"types": "./dist/fs/base.d.ts", "import": "./dist/fs/base.js", "require": "./dist/fs/base.cjs"}, "./contrib/*": {"types": "./dist/contrib/*.d.ts", "import": "./dist/contrib/*.js", "require": "./dist/contrib/*.cjs"}}, "type": "module", "types": "dist/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/electric-sql/pglite", "directory": "packages/pglite"}, "devDependencies": {"@types/emscripten": "^1.39.13", "@types/node": "^20.16.11", "@types/node-fetch": "^2.6.11", "async-mutex": "^0.4.1", "buffer": "^6.0.3", "bun": "^1.1.30", "concurrently": "^8.2.2", "http-server": "^14.1.1", "playwright": "^1.48.0", "tinytar": "^0.1.0", "vitest": "^2.1.2", "@electric-sql/pg-protocol": "0.0.2"}, "browser": {"fs": false, "fs/promises": false, "path": false, "url": false, "zlib": false, "stream": false, "stream/promises": false, "crypto": false, "ws": false, "child_process": false, "module": false, "util": false}, "scripts": {"test": "pnpm test:basic && pnpm test:node", "test:basic": "pnpm test:clean && vitest tests/*.test.js tests/*.test.ts tests/**/*.test.js tests/**/*.test.ts", "test:web": "pnpm test:clean && concurrently -s first --hide 1 --prefix none -k \"sleep 2 && vitest --fileParallelism false tests/targets/web/*.test.web.*\" \"npx http-server --port 3334 ./\"", "test:bun": "pnpm test:clean && pnpm bun test --timeout 15000 tests/basic.test.js tests/pgvector.test.js tests/live.test.js tests/targets/runtimes/node-fs.test.js", "test:deno": "cd tests/targets/deno && deno task test", "test:node": "pnpm test:clean && pnpm vitest tests/targets/runtimes/node-*.test.js", "test:runtimes": "pnpm test:bun && pnpm test:node", "test:integration": "pnpm test:runtimes && pnpm test:web", "test:clean": "rm -rf ./pgdata-test", "build:js": "tsup && tsx scripts/bundle-wasm.ts", "build": "pnpm build:js", "dev": "concurrently \"tsup --watch\" \"sleep 1 && tsx scripts/bundle-wasm.ts\" \"pnpm dev-server\"", "dev-server": "pnpm http-server ../", "lint": "eslint ./src ./tests --report-unused-disable-directives --max-warnings 0", "format": "prettier --write ./src ./tests", "typecheck": "tsc --noEmit", "stylecheck": "pnpm lint && prettier --check ./src ./tests"}}