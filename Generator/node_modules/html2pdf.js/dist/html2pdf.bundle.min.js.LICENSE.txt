/*!
 * @overview es6-promise - a tiny implementation of Promises/A+.
 * @copyright Copyright (c) 2014 <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors (Conversion to ES6 API by <PERSON>)
 * @license   Licensed under MIT license
 *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
 * @version   v4.2.8+1e68dce6
 */

/*!
 * html2canvas 1.0.0 <https://html2canvas.hertzen.com>
 * Copyright (c) 2021 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 */

/*!
 * html2pdf.js v0.10.3
 * Copyright (c) 2025 <PERSON>
 * Released under the MIT License.
 */

/*! *****************************************************************************
    Copyright (c) Microsoft Corporation. All rights reserved.
    Licensed under the Apache License, Version 2.0 (the "License"); you may not use
    this file except in compliance with the License. You may obtain a copy of the
    License at http://www.apache.org/licenses/LICENSE-2.0

    THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
    KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
    WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
    MERCHANTABLITY OR NON-INFRINGEMENT.

    See the Apache Version 2.0 License for specific language governing permissions
    and limitations under the License.
    ***************************************************************************** */

/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */

/*! ../../../es/array/virtual/for-each */

/*! ../../../es/array/virtual/values */

/*! ../../../internals/entry-virtual */

/*! ../../../modules/es.array.concat */

/*! ../../../modules/es.array.every */

/*! ../../../modules/es.array.fill */

/*! ../../../modules/es.array.filter */

/*! ../../../modules/es.array.for-each */

/*! ../../../modules/es.array.includes */

/*! ../../../modules/es.array.index-of */

/*! ../../../modules/es.array.iterator */

/*! ../../../modules/es.array.map */

/*! ../../../modules/es.array.reduce */

/*! ../../../modules/es.array.reverse */

/*! ../../../modules/es.array.slice */

/*! ../../../modules/es.array.some */

/*! ../../../modules/es.function.bind */

/*! ../../../modules/es.string.includes */

/*! ../../../modules/es.string.starts-with */

/*! ../../../modules/es.string.trim */

/*! ../../es/array/from */

/*! ../../es/array/is-array */

/*! ../../es/date/now */

/*! ../../es/instance/bind */

/*! ../../es/instance/concat */

/*! ../../es/instance/every */

/*! ../../es/instance/fill */

/*! ../../es/instance/filter */

/*! ../../es/instance/includes */

/*! ../../es/instance/index-of */

/*! ../../es/instance/map */

/*! ../../es/instance/reduce */

/*! ../../es/instance/reverse */

/*! ../../es/instance/slice */

/*! ../../es/instance/some */

/*! ../../es/instance/starts-with */

/*! ../../es/instance/trim */

/*! ../../es/map */

/*! ../../es/object/create */

/*! ../../es/object/define-properties */

/*! ../../es/object/define-property */

/*! ../../es/object/get-own-property-descriptor */

/*! ../../es/object/get-own-property-descriptors */

/*! ../../es/object/get-own-property-symbols */

/*! ../../es/object/get-prototype-of */

/*! ../../es/object/keys */

/*! ../../es/object/set-prototype-of */

/*! ../../es/promise */

/*! ../../es/reflect/apply */

/*! ../../es/reflect/construct */

/*! ../../es/reflect/delete-property */

/*! ../../es/reflect/get */

/*! ../../es/reflect/get-prototype-of */

/*! ../../es/symbol */

/*! ../../es/symbol/iterator */

/*! ../../internals/classof */

/*! ../../internals/path */

/*! ../../internals/well-known-symbol-wrapped */

/*! ../../modules/es.aggregate-error */

/*! ../../modules/es.array.concat */

/*! ../../modules/es.array.from */

/*! ../../modules/es.array.is-array */

/*! ../../modules/es.date.now */

/*! ../../modules/es.json.to-string-tag */

/*! ../../modules/es.map */

/*! ../../modules/es.math.to-string-tag */

/*! ../../modules/es.object.create */

/*! ../../modules/es.object.define-properties */

/*! ../../modules/es.object.define-property */

/*! ../../modules/es.object.get-own-property-descriptor */

/*! ../../modules/es.object.get-own-property-descriptors */

/*! ../../modules/es.object.get-prototype-of */

/*! ../../modules/es.object.keys */

/*! ../../modules/es.object.set-prototype-of */

/*! ../../modules/es.object.to-string */

/*! ../../modules/es.promise */

/*! ../../modules/es.promise.all-settled */

/*! ../../modules/es.promise.any */

/*! ../../modules/es.promise.finally */

/*! ../../modules/es.reflect.apply */

/*! ../../modules/es.reflect.construct */

/*! ../../modules/es.reflect.delete-property */

/*! ../../modules/es.reflect.get */

/*! ../../modules/es.reflect.get-prototype-of */

/*! ../../modules/es.reflect.to-string-tag */

/*! ../../modules/es.string.iterator */

/*! ../../modules/es.symbol */

/*! ../../modules/es.symbol.async-iterator */

/*! ../../modules/es.symbol.description */

/*! ../../modules/es.symbol.has-instance */

/*! ../../modules/es.symbol.is-concat-spreadable */

/*! ../../modules/es.symbol.iterator */

/*! ../../modules/es.symbol.match */

/*! ../../modules/es.symbol.match-all */

/*! ../../modules/es.symbol.replace */

/*! ../../modules/es.symbol.search */

/*! ../../modules/es.symbol.species */

/*! ../../modules/es.symbol.split */

/*! ../../modules/es.symbol.to-primitive */

/*! ../../modules/es.symbol.to-string-tag */

/*! ../../modules/es.symbol.unscopables */

/*! ../../modules/esnext.aggregate-error */

/*! ../../modules/esnext.promise.all-settled */

/*! ../../modules/esnext.promise.any */

/*! ../../modules/esnext.promise.try */

/*! ../../modules/esnext.symbol.async-dispose */

/*! ../../modules/esnext.symbol.dispose */

/*! ../../modules/esnext.symbol.matcher */

/*! ../../modules/esnext.symbol.metadata */

/*! ../../modules/esnext.symbol.observable */

/*! ../../modules/esnext.symbol.pattern-match */

/*! ../../modules/esnext.symbol.replace-all */

/*! ../../modules/web.dom-collections.iterator */

/*! ../array/virtual/concat */

/*! ../array/virtual/every */

/*! ../array/virtual/fill */

/*! ../array/virtual/filter */

/*! ../array/virtual/for-each */

/*! ../array/virtual/includes */

/*! ../array/virtual/index-of */

/*! ../array/virtual/map */

/*! ../array/virtual/reduce */

/*! ../array/virtual/reverse */

/*! ../array/virtual/slice */

/*! ../array/virtual/some */

/*! ../array/virtual/values */

/*! ../es/parse-float */

/*! ../es/parse-int */

/*! ../function/virtual/bind */

/*! ../internals/a-function */

/*! ../internals/a-possible-prototype */

/*! ../internals/add-to-unscopables */

/*! ../internals/an-instance */

/*! ../internals/an-object */

/*! ../internals/array-fill */

/*! ../internals/array-for-each */

/*! ../internals/array-from */

/*! ../internals/array-includes */

/*! ../internals/array-iteration */

/*! ../internals/array-method-has-species-support */

/*! ../internals/array-method-is-strict */

/*! ../internals/array-reduce */

/*! ../internals/array-species-constructor */

/*! ../internals/array-species-create */

/*! ../internals/call-with-safe-iteration-closing */

/*! ../internals/check-correctness-of-iteration */

/*! ../internals/classof */

/*! ../internals/classof-raw */

/*! ../internals/collection */

/*! ../internals/collection-strong */

/*! ../internals/copy-constructor-properties */

/*! ../internals/correct-is-regexp-logic */

/*! ../internals/correct-prototype-getter */

/*! ../internals/create-html */

/*! ../internals/create-iterator-constructor */

/*! ../internals/create-non-enumerable-property */

/*! ../internals/create-property */

/*! ../internals/create-property-descriptor */

/*! ../internals/define-iterator */

/*! ../internals/define-well-known-symbol */

/*! ../internals/descriptors */

/*! ../internals/document-create-element */

/*! ../internals/dom-iterables */

/*! ../internals/engine-is-browser */

/*! ../internals/engine-is-ios */

/*! ../internals/engine-is-node */

/*! ../internals/engine-is-webos-webkit */

/*! ../internals/engine-user-agent */

/*! ../internals/engine-v8-version */

/*! ../internals/enum-bug-keys */

/*! ../internals/export */

/*! ../internals/fails */

/*! ../internals/freezing */

/*! ../internals/function-bind */

/*! ../internals/function-bind-context */

/*! ../internals/get-built-in */

/*! ../internals/get-iterator */

/*! ../internals/get-iterator-method */

/*! ../internals/global */

/*! ../internals/has */

/*! ../internals/hidden-keys */

/*! ../internals/host-report-errors */

/*! ../internals/html */

/*! ../internals/ie8-dom-define */

/*! ../internals/indexed-object */

/*! ../internals/inherit-if-required */

/*! ../internals/inspect-source */

/*! ../internals/internal-metadata */

/*! ../internals/internal-state */

/*! ../internals/is-array */

/*! ../internals/is-array-iterator-method */

/*! ../internals/is-forced */

/*! ../internals/is-object */

/*! ../internals/is-pure */

/*! ../internals/is-regexp */

/*! ../internals/is-symbol */

/*! ../internals/iterate */

/*! ../internals/iterator-close */

/*! ../internals/iterators */

/*! ../internals/iterators-core */

/*! ../internals/microtask */

/*! ../internals/native-promise-constructor */

/*! ../internals/native-symbol */

/*! ../internals/native-weak-map */

/*! ../internals/new-promise-capability */

/*! ../internals/not-a-regexp */

/*! ../internals/number-parse-float */

/*! ../internals/number-parse-int */

/*! ../internals/object-assign */

/*! ../internals/object-create */

/*! ../internals/object-define-properties */

/*! ../internals/object-define-property */

/*! ../internals/object-get-own-property-descriptor */

/*! ../internals/object-get-own-property-names */

/*! ../internals/object-get-own-property-names-external */

/*! ../internals/object-get-own-property-symbols */

/*! ../internals/object-get-prototype-of */

/*! ../internals/object-keys */

/*! ../internals/object-keys-internal */

/*! ../internals/object-property-is-enumerable */

/*! ../internals/object-set-prototype-of */

/*! ../internals/object-to-string */

/*! ../internals/ordinary-to-primitive */

/*! ../internals/own-keys */

/*! ../internals/path */

/*! ../internals/perform */

/*! ../internals/promise-resolve */

/*! ../internals/redefine */

/*! ../internals/redefine-all */

/*! ../internals/regexp-flags */

/*! ../internals/require-object-coercible */

/*! ../internals/set-global */

/*! ../internals/set-species */

/*! ../internals/set-to-string-tag */

/*! ../internals/shared */

/*! ../internals/shared-key */

/*! ../internals/shared-store */

/*! ../internals/species-constructor */

/*! ../internals/string-html-forced */

/*! ../internals/string-multibyte */

/*! ../internals/string-trim */

/*! ../internals/string-trim-forced */

/*! ../internals/task */

/*! ../internals/to-absolute-index */

/*! ../internals/to-indexed-object */

/*! ../internals/to-integer */

/*! ../internals/to-length */

/*! ../internals/to-object */

/*! ../internals/to-primitive */

/*! ../internals/to-property-key */

/*! ../internals/to-string */

/*! ../internals/to-string-tag-support */

/*! ../internals/uid */

/*! ../internals/use-symbol-as-uid */

/*! ../internals/well-known-symbol */

/*! ../internals/well-known-symbol-wrapped */

/*! ../internals/whitespaces */

/*! ../modules/es.array.iterator */

/*! ../modules/es.parse-float */

/*! ../modules/es.parse-int */

/*! ../modules/es.string.iterator */

/*! ../modules/web.dom-collections.iterator */

/*! ../string/virtual/includes */

/*! ../string/virtual/starts-with */

/*! ../string/virtual/trim */

/*! ../utils.js */

/*! ../worker.js */

/*! ./arrayLikeToArray.js */

/*! ./arrayWithHoles.js */

/*! ./arrayWithoutHoles.js */

/*! ./assertThisInitialized.js */

/*! ./es.aggregate-error */

/*! ./es.array.iterator */

/*! ./es.promise.all-settled.js */

/*! ./es.promise.any */

/*! ./export */

/*! ./getPrototypeOf.js */

/*! ./iterableToArray.js */

/*! ./iterableToArrayLimit.js */

/*! ./nonIterableRest.js */

/*! ./nonIterableSpread.js */

/*! ./plugin/hyperlinks.js */

/*! ./plugin/jspdf-plugin.js */

/*! ./plugin/pagebreaks.js */

/*! ./setPrototypeOf.js */

/*! ./superPropBase.js */

/*! ./unsupportedIterableToArray.js */

/*! ./utils.js */

/*! ./worker.js */

/*! @babel/runtime-corejs3/core-js-stable/array/from */

/*! @babel/runtime-corejs3/core-js-stable/array/is-array */

/*! @babel/runtime-corejs3/core-js-stable/date/now */

/*! @babel/runtime-corejs3/core-js-stable/instance/bind */

/*! @babel/runtime-corejs3/core-js-stable/instance/concat */

/*! @babel/runtime-corejs3/core-js-stable/instance/every */

/*! @babel/runtime-corejs3/core-js-stable/instance/fill */

/*! @babel/runtime-corejs3/core-js-stable/instance/filter */

/*! @babel/runtime-corejs3/core-js-stable/instance/for-each */

/*! @babel/runtime-corejs3/core-js-stable/instance/includes */

/*! @babel/runtime-corejs3/core-js-stable/instance/index-of */

/*! @babel/runtime-corejs3/core-js-stable/instance/map */

/*! @babel/runtime-corejs3/core-js-stable/instance/reduce */

/*! @babel/runtime-corejs3/core-js-stable/instance/reverse */

/*! @babel/runtime-corejs3/core-js-stable/instance/slice */

/*! @babel/runtime-corejs3/core-js-stable/instance/some */

/*! @babel/runtime-corejs3/core-js-stable/instance/starts-with */

/*! @babel/runtime-corejs3/core-js-stable/instance/trim */

/*! @babel/runtime-corejs3/core-js-stable/instance/values */

/*! @babel/runtime-corejs3/core-js-stable/map */

/*! @babel/runtime-corejs3/core-js-stable/object/define-properties */

/*! @babel/runtime-corejs3/core-js-stable/object/define-property */

/*! @babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptor */

/*! @babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptors */

/*! @babel/runtime-corejs3/core-js-stable/object/get-own-property-symbols */

/*! @babel/runtime-corejs3/core-js-stable/object/keys */

/*! @babel/runtime-corejs3/core-js-stable/parse-float */

/*! @babel/runtime-corejs3/core-js-stable/parse-int */

/*! @babel/runtime-corejs3/core-js-stable/promise */

/*! @babel/runtime-corejs3/core-js-stable/reflect/apply */

/*! @babel/runtime-corejs3/core-js-stable/reflect/construct */

/*! @babel/runtime-corejs3/core-js-stable/reflect/delete-property */

/*! @babel/runtime-corejs3/core-js-stable/reflect/get-prototype-of */

/*! @babel/runtime-corejs3/core-js-stable/symbol */

/*! @babel/runtime-corejs3/core-js/array/from */

/*! @babel/runtime-corejs3/core-js/array/is-array */

/*! @babel/runtime-corejs3/core-js/get-iterator */

/*! @babel/runtime-corejs3/core-js/get-iterator-method */

/*! @babel/runtime-corejs3/core-js/instance/slice */

/*! @babel/runtime-corejs3/core-js/object/create */

/*! @babel/runtime-corejs3/core-js/object/define-property */

/*! @babel/runtime-corejs3/core-js/object/get-own-property-descriptor */

/*! @babel/runtime-corejs3/core-js/object/get-prototype-of */

/*! @babel/runtime-corejs3/core-js/object/set-prototype-of */

/*! @babel/runtime-corejs3/core-js/promise */

/*! @babel/runtime-corejs3/core-js/reflect/get */

/*! @babel/runtime-corejs3/core-js/symbol */

/*! @babel/runtime-corejs3/core-js/symbol/iterator */

/*! @babel/runtime-corejs3/helpers/assertThisInitialized */

/*! @babel/runtime-corejs3/helpers/asyncToGenerator */

/*! @babel/runtime-corejs3/helpers/classCallCheck */

/*! @babel/runtime-corejs3/helpers/createClass */

/*! @babel/runtime-corejs3/helpers/defineProperty */

/*! @babel/runtime-corejs3/helpers/get */

/*! @babel/runtime-corejs3/helpers/getPrototypeOf */

/*! @babel/runtime-corejs3/helpers/inherits */

/*! @babel/runtime-corejs3/helpers/possibleConstructorReturn */

/*! @babel/runtime-corejs3/helpers/slicedToArray */

/*! @babel/runtime-corejs3/helpers/toConsumableArray */

/*! @babel/runtime-corejs3/helpers/typeof */

/*! @babel/runtime-corejs3/regenerator */

/*! @babel/runtime/helpers/typeof */

/*! @license DOMPurify 3.2.4 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.4/LICENSE */

/*! canvg */

/*! core-js-pure/features/array/from */

/*! core-js-pure/features/array/is-array */

/*! core-js-pure/features/get-iterator */

/*! core-js-pure/features/get-iterator-method */

/*! core-js-pure/features/instance/slice */

/*! core-js-pure/features/object/create */

/*! core-js-pure/features/object/define-property */

/*! core-js-pure/features/object/get-own-property-descriptor */

/*! core-js-pure/features/object/get-prototype-of */

/*! core-js-pure/features/object/set-prototype-of */

/*! core-js-pure/features/promise */

/*! core-js-pure/features/reflect/get */

/*! core-js-pure/features/symbol */

/*! core-js-pure/features/symbol/iterator */

/*! core-js-pure/stable/array/from */

/*! core-js-pure/stable/array/is-array */

/*! core-js-pure/stable/date/now */

/*! core-js-pure/stable/instance/bind */

/*! core-js-pure/stable/instance/concat */

/*! core-js-pure/stable/instance/every */

/*! core-js-pure/stable/instance/fill */

/*! core-js-pure/stable/instance/filter */

/*! core-js-pure/stable/instance/for-each */

/*! core-js-pure/stable/instance/includes */

/*! core-js-pure/stable/instance/index-of */

/*! core-js-pure/stable/instance/map */

/*! core-js-pure/stable/instance/reduce */

/*! core-js-pure/stable/instance/reverse */

/*! core-js-pure/stable/instance/slice */

/*! core-js-pure/stable/instance/some */

/*! core-js-pure/stable/instance/starts-with */

/*! core-js-pure/stable/instance/trim */

/*! core-js-pure/stable/instance/values */

/*! core-js-pure/stable/map */

/*! core-js-pure/stable/object/define-properties */

/*! core-js-pure/stable/object/define-property */

/*! core-js-pure/stable/object/get-own-property-descriptor */

/*! core-js-pure/stable/object/get-own-property-descriptors */

/*! core-js-pure/stable/object/get-own-property-symbols */

/*! core-js-pure/stable/object/keys */

/*! core-js-pure/stable/parse-float */

/*! core-js-pure/stable/parse-int */

/*! core-js-pure/stable/promise */

/*! core-js-pure/stable/reflect/apply */

/*! core-js-pure/stable/reflect/construct */

/*! core-js-pure/stable/reflect/delete-property */

/*! core-js-pure/stable/reflect/get-prototype-of */

/*! core-js-pure/stable/symbol */

/*! core-js/modules/es.array.concat.js */

/*! core-js/modules/es.array.iterator.js */

/*! core-js/modules/es.array.join.js */

/*! core-js/modules/es.array.map.js */

/*! core-js/modules/es.array.slice.js */

/*! core-js/modules/es.function.name.js */

/*! core-js/modules/es.number.constructor.js */

/*! core-js/modules/es.object.assign.js */

/*! core-js/modules/es.object.keys.js */

/*! core-js/modules/es.object.to-string.js */

/*! core-js/modules/es.regexp.to-string.js */

/*! core-js/modules/es.string.iterator.js */

/*! core-js/modules/es.string.link.js */

/*! core-js/modules/es.symbol.description.js */

/*! core-js/modules/es.symbol.iterator.js */

/*! core-js/modules/es.symbol.js */

/*! core-js/modules/web.dom-collections.for-each.js */

/*! core-js/modules/web.dom-collections.iterator.js */

/*! dompurify */

/*! es6-promise */

/*! fflate */

/*! html2canvas */

/*! jspdf */

/*! performance-now */

/*! raf */

/*! regenerator-runtime */

/*! rgbcolor */

/*! stackblur-canvas */

/*! svg-pathdata */

/*!**********************!*\
  !*** ./src/index.js ***!
  \**********************/

/*!**********************!*\
  !*** ./src/utils.js ***!
  \**********************/

/*!***********************!*\
  !*** ./src/worker.js ***!
  \***********************/

/*!**********************************!*\
  !*** ./src/plugin/hyperlinks.js ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/plugin/pagebreaks.js ***!
  \**********************************/

/*!***********************************!*\
  !*** ./node_modules/raf/index.js ***!
  \***********************************/

/*!************************************!*\
  !*** ./src/plugin/jspdf-plugin.js ***!
  \************************************/

/*!****************************************!*\
  !*** ./node_modules/rgbcolor/index.js ***!
  \****************************************/

/*!********************************************!*\
  !*** ./node_modules/canvg/lib/index.es.js ***!
  \********************************************/

/*!********************************************!*\
  !*** ./node_modules/fflate/esm/browser.js ***!
  \********************************************/

/*!***********************************************!*\
  !*** ./node_modules/core-js/internals/has.js ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./node_modules/core-js/internals/uid.js ***!
  \***********************************************/

/*!************************************************!*\
  !*** ./node_modules/core-js/internals/html.js ***!
  \************************************************/

/*!************************************************!*\
  !*** ./node_modules/core-js/internals/path.js ***!
  \************************************************/

/*!*************************************************!*\
  !*** ./node_modules/core-js/internals/fails.js ***!
  \*************************************************/

/*!*************************************************!*\
  !*** ./node_modules/jspdf/dist/jspdf.es.min.js ***!
  \*************************************************/

/*!**************************************************!*\
  !*** ./node_modules/core-js-pure/es/date/now.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./node_modules/core-js/internals/export.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./node_modules/core-js/internals/global.js ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./node_modules/core-js/internals/shared.js ***!
  \**************************************************/

/*!***************************************************!*\
  !*** ./node_modules/core-js-pure/es/map/index.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/core-js-pure/es/parse-int.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/core-js/internals/classof.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/core-js/internals/is-pure.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/core-js/modules/es.symbol.js ***!
  \***************************************************/

/*!***************************************************!*\
  !*** ./node_modules/dompurify/dist/purify.es.mjs ***!
  \***************************************************/

/*!****************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/from.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./node_modules/core-js-pure/internals/has.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./node_modules/core-js-pure/internals/uid.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./node_modules/core-js/internals/is-array.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./node_modules/core-js/internals/own-keys.js ***!
  \****************************************************/

/*!****************************************************!*\
  !*** ./node_modules/core-js/internals/redefine.js ***!
  \****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js-pure/es/object/keys.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js-pure/es/parse-float.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js-pure/es/reflect/get.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js-pure/internals/html.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js-pure/internals/path.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js-pure/internals/task.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.map.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/an-object.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/is-forced.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/is-object.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/is-symbol.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/iterators.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/to-length.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/to-object.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/to-string.js ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./node_modules/regenerator-runtime/runtime.js ***!
  \*****************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/map.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js-pure/es/symbol/index.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js-pure/internals/fails.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js-pure/stable/date/now.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js/internals/a-function.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js/internals/set-global.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js/internals/shared-key.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js/internals/to-integer.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.map.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/es6-promise/dist/es6-promise.js ***!
  \******************************************************/

/*!******************************************************!*\
  !*** ./node_modules/html2canvas/dist/html2canvas.js ***!
  \******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/bind.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/fill.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/some.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/trim.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js-pure/es/object/create.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js-pure/es/promise/index.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js-pure/es/reflect/apply.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js-pure/internals/export.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js-pure/internals/global.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js-pure/internals/shared.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js-pure/stable/map/index.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js-pure/stable/parse-int.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/classof-raw.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/create-html.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/descriptors.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/hidden-keys.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/object-keys.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/string-trim.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/whitespaces.js ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.join.js ***!
  \*******************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/is-array.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/every.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/slice.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/classof.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/is-pure.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/iterate.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/perform.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js-pure/stable/array/from.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/internals/get-built-in.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/internals/regexp-flags.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/internals/shared-store.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/internals/to-primitive.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.slice.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/modules/es.object.keys.js ***!
  \********************************************************/

/*!********************************************************!*\
  !*** ./node_modules/core-js/modules/es.string.link.js ***!
  \********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/concat.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/filter.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/reduce.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js-pure/es/symbol/iterator.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/freezing.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/is-array.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/own-keys.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/redefine.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.promise.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js-pure/stable/object/keys.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js-pure/stable/parse-float.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/dom-iterables.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/enum-bug-keys.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/native-symbol.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/object-assign.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/object-create.js ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.concat.js ***!
  \*********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/reverse.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/features/array/from.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/an-object.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/is-forced.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/is-object.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/is-regexp.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/iterators.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/microtask.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/to-length.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/to-object.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.date.now.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/map.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js-pure/stable/symbol/index.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/array-for-each.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/array-includes.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/ie8-dom-define.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/indexed-object.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/inspect-source.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/internal-state.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/iterators-core.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/modules/es.function.name.js ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./node_modules/core-js/modules/es.object.assign.js ***!
  \**********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/esm/typeof.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/map.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/includes.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/index-of.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/es/reflect/construct.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/features/reflect/get.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/a-function.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/array-fill.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/array-from.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/collection.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/set-global.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/shared-key.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/internals/to-integer.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.map.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.parse-int.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/bind.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/fill.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/some.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/trim.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/stable/promise/index.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js-pure/stable/reflect/apply.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/array-iteration.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/create-property.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/define-iterator.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/native-weak-map.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/to-property-key.js ***!
  \***********************************************************/

/*!***********************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.iterator.js ***!
  \***********************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/fill.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/some.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/features/get-iterator.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/features/symbol/index.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/an-instance.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/classof-raw.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/descriptors.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/hidden-keys.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-keys.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/set-species.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/string-trim.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/whitespaces.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.fill.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.from.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.some.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/array/is-array.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/every.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/slice.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js/internals/object-to-string.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js/internals/string-multibyte.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js/modules/es.string.iterator.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/core-js/modules/es.symbol.iterator.js ***!
  \************************************************************/

/*!************************************************************!*\
  !*** ./node_modules/stackblur-canvas/dist/stackblur-es.js ***!
  \************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/every.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/slice.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/es/string/virtual/trim.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/features/object/create.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/features/promise/index.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/array-reduce.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/get-built-in.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/get-iterator.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/not-a-regexp.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/redefine-all.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/shared-store.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/to-primitive.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.every.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.slice.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.object.keys.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.parse-float.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.promise.any.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.reflect.get.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.string.trim.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/concat.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/filter.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/reduce.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/values.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/engine-user-agent.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/engine-v8-version.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/set-to-string-tag.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/to-absolute-index.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/to-indexed-object.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/use-symbol-as-uid.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/well-known-symbol.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/modules/es.object.to-string.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/core-js/modules/es.regexp.to-string.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/performance-now/lib/performance-now.js ***!
  \*************************************************************/

/*!*************************************************************!*\
  !*** ./node_modules/svg-pathdata/lib/SVGPathData.module.js ***!
  \*************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/concat.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/filter.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/reduce.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/values.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/es/instance/starts-with.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/features/array/is-array.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/features/instance/slice.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/dom-iterables.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/engine-is-ios.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/entry-virtual.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/enum-bug-keys.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/function-bind.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/native-symbol.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-create.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.concat.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.filter.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.reduce.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.match.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.split.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/reverse.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js/internals/add-to-unscopables.js ***!
  \**************************************************************/

/*!**************************************************************!*\
  !*** ./node_modules/core-js/internals/string-html-forced.js ***!
  \**************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/symbol.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/reverse.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/es/function/virtual/bind.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/features/symbol/iterator.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/array-for-each.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/array-includes.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/engine-is-node.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/ie8-dom-define.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/indexed-object.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/inspect-source.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/internal-state.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/iterator-close.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/iterators-core.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.reverse.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.function.bind.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.object.create.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.reflect.apply.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.search.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/for-each.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/includes.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/index-of.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/reflect/construct.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js/internals/inherit-if-required.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js/modules/es.number.constructor.js ***!
  \***************************************************************/

/*!***************************************************************!*\
  !*** ./node_modules/core-js/modules/es.symbol.description.js ***!
  \***************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/promise.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/get.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/for-each.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/includes.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/es/array/virtual/index-of.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/es/object/define-property.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/array-iteration.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/create-property.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/define-iterator.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/native-weak-map.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/promise-resolve.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.for-each.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.includes.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.index-of.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.is-array.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.array.iterator.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.replace.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.species.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js/internals/a-possible-prototype.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js/internals/array-species-create.js ***!
  \****************************************************************/

/*!****************************************************************!*\
  !*** ./node_modules/core-js/internals/object-keys-internal.js ***!
  \****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/es/object/get-prototype-of.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/es/object/set-prototype-of.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/es/reflect/delete-property.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/es/string/virtual/includes.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/number-parse-int.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-to-string.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/string-multibyte.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.aggregate-error.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.promise.finally.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.string.includes.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.string.iterator.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.iterator.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/esnext.promise.any.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/esnext.promise.try.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js/internals/function-bind-context.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js/internals/ordinary-to-primitive.js ***!
  \*****************************************************************/

/*!*****************************************************************!*\
  !*** ./node_modules/core-js/internals/to-string-tag-support.js ***!
  \*****************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/regenerator/index.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/es/object/define-properties.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/es/reflect/get-prototype-of.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/collection-strong.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/engine-is-browser.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/engine-user-agent.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/engine-v8-version.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/internal-metadata.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/set-to-string-tag.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/to-absolute-index.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/to-indexed-object.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/use-symbol-as-uid.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/well-known-symbol.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.object.to-string.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.match-all.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/array/virtual/values.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/instance/starts-with.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js/internals/array-method-is-strict.js ***!
  \******************************************************************/

/*!******************************************************************!*\
  !*** ./node_modules/core-js/internals/object-define-property.js ***!
  \******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/map.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/array/from.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/typeof.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/core-js-pure/features/get-iterator-method.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/add-to-unscopables.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/host-report-errors.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/number-parse-float.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/string-trim-forced.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.reflect.construct.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/core-js/internals/document-create-element.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-prototype-of.js ***!
  \*******************************************************************/

/*!*******************************************************************!*\
  !*** ./node_modules/core-js/internals/object-set-prototype-of.js ***!
  \*******************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/reflect/get.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js-pure/es/string/virtual/starts-with.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/get-iterator-method.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/species-constructor.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.json.to-string-tag.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.math.to-string-tag.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.string.starts-with.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.description.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.unscopables.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/esnext.symbol.dispose.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/esnext.symbol.matcher.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/array/virtual/for-each.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/object/define-property.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js/internals/correct-prototype-getter.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js/internals/define-well-known-symbol.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js/internals/object-define-properties.js ***!
  \********************************************************************/

/*!********************************************************************!*\
  !*** ./node_modules/core-js/internals/require-object-coercible.js ***!
  \********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/get-iterator.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/inherits.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/a-possible-prototype.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/array-species-create.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-keys-internal.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.promise.all-settled.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.has-instance.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.to-primitive.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/esnext.aggregate-error.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/esnext.symbol.metadata.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/reflect/delete-property.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js/internals/array-species-constructor.js ***!
  \*********************************************************************/

/*!*********************************************************************!*\
  !*** ./node_modules/core-js/internals/well-known-symbol-wrapped.js ***!
  \*********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/symbol.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/object/create.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/core-js-pure/features/object/define-property.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/function-bind-context.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/to-string-tag-support.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.to-string-tag.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/object/define-properties.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/reflect/get-prototype-of.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/core-js/internals/create-property-descriptor.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/core-js/modules/web.dom-collections.for-each.js ***!
  \**********************************************************************/

/*!**********************************************************************!*\
  !*** ./node_modules/core-js/modules/web.dom-collections.iterator.js ***!
  \**********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/promise.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/array/is-array.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/instance/slice.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js-pure/features/object/get-prototype-of.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js-pure/features/object/set-prototype-of.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/array-method-is-strict.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/engine-is-webos-webkit.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/new-promise-capability.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-define-property.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.reflect.to-string-tag.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.async-iterator.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/esnext.symbol.observable.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js/internals/copy-constructor-properties.js ***!
  \***********************************************************************/

/*!***********************************************************************!*\
  !*** ./node_modules/core-js/internals/create-iterator-constructor.js ***!
  \***********************************************************************/

/*!************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/date/now.js ***!
  \************************************************************************/

/*!************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/symbol/iterator.js ***!
  \************************************************************************/

/*!************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/createClass.js ***!
  \************************************************************************/

/*!************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/correct-is-regexp-logic.js ***!
  \************************************************************************/

/*!************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/document-create-element.js ***!
  \************************************************************************/

/*!************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-get-prototype-of.js ***!
  \************************************************************************/

/*!************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-set-prototype-of.js ***!
  \************************************************************************/

/*!************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.object.define-property.js ***!
  \************************************************************************/

/*!************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/esnext.symbol.replace-all.js ***!
  \************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/parse-int.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js-pure/es/object/get-own-property-symbols.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/correct-prototype-getter.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/define-well-known-symbol.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/is-array-iterator-method.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-define-properties.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/require-object-coercible.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.object.get-prototype-of.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.object.set-prototype-of.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.reflect.delete-property.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/esnext.promise.all-settled.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-own-property-names.js ***!
  \*************************************************************************/

/*!*************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-property-is-enumerable.js ***!
  \*************************************************************************/

/*!**************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/array/from.js ***!
  \**************************************************************************/

/*!**************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/slicedToArray.js ***!
  \**************************************************************************/

/*!**************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/superPropBase.js ***!
  \**************************************************************************/

/*!**************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/well-known-symbol-wrapped.js ***!
  \**************************************************************************/

/*!**************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.object.define-properties.js ***!
  \**************************************************************************/

/*!**************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.reflect.get-prototype-of.js ***!
  \**************************************************************************/

/*!**************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/esnext.symbol.async-dispose.js ***!
  \**************************************************************************/

/*!**************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/esnext.symbol.pattern-match.js ***!
  \**************************************************************************/

/*!**************************************************************************!*\
  !*** ./node_modules/core-js/internals/create-non-enumerable-property.js ***!
  \**************************************************************************/

/*!***************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/object/keys.js ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/parse-float.js ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/arrayWithHoles.js ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/classCallCheck.js ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/defineProperty.js ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/getPrototypeOf.js ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/setPrototypeOf.js ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/create-property-descriptor.js ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/native-promise-constructor.js ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/web.dom-collections.iterator.js ***!
  \***************************************************************************/

/*!***************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-own-property-symbols.js ***!
  \***************************************************************************/

/*!****************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/map.js ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/get-iterator-method.js ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/iterableToArray.js ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/nonIterableRest.js ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ./node_modules/core-js-pure/es/object/get-own-property-descriptor.js ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/create-iterator-constructor.js ***!
  \****************************************************************************/

/*!****************************************************************************!*\
  !*** ./node_modules/core-js/internals/array-method-has-species-support.js ***!
  \****************************************************************************/

/*!*****************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/bind.js ***!
  \*****************************************************************************/

/*!*****************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/fill.js ***!
  \*****************************************************************************/

/*!*****************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/some.js ***!
  \*****************************************************************************/

/*!*****************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/trim.js ***!
  \*****************************************************************************/

/*!*****************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/reflect/apply.js ***!
  \*****************************************************************************/

/*!*****************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/arrayLikeToArray.js ***!
  \*****************************************************************************/

/*!*****************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/asyncToGenerator.js ***!
  \*****************************************************************************/

/*!*****************************************************************************!*\
  !*** ./node_modules/core-js-pure/es/object/get-own-property-descriptors.js ***!
  \*****************************************************************************/

/*!*****************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.symbol.is-concat-spreadable.js ***!
  \*****************************************************************************/

/*!*****************************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/object/get-own-property-symbols.js ***!
  \*****************************************************************************/

/*!******************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/array/is-array.js ***!
  \******************************************************************************/

/*!******************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/every.js ***!
  \******************************************************************************/

/*!******************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/slice.js ***!
  \******************************************************************************/

/*!******************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/arrayWithoutHoles.js ***!
  \******************************************************************************/

/*!******************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/nonIterableSpread.js ***!
  \******************************************************************************/

/*!******************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/toConsumableArray.js ***!
  \******************************************************************************/

/*!******************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-get-own-property-names.js ***!
  \******************************************************************************/

/*!******************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-property-is-enumerable.js ***!
  \******************************************************************************/

/*!******************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-own-property-descriptor.js ***!
  \******************************************************************************/

/*!*******************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/concat.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/filter.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/reduce.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/values.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/object/define-property.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/check-correctness-of-iteration.js ***!
  \*******************************************************************************/

/*!*******************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/create-non-enumerable-property.js ***!
  \*******************************************************************************/

/*!********************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/reverse.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/object/get-prototype-of.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/object/set-prototype-of.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-get-own-property-symbols.js ***!
  \********************************************************************************/

/*!********************************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/object/get-own-property-descriptor.js ***!
  \********************************************************************************/

/*!*********************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/for-each.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/includes.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/index-of.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/reflect/construct.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/iterableToArrayLimit.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/array-method-has-species-support.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/call-with-safe-iteration-closing.js ***!
  \*********************************************************************************/

/*!*********************************************************************************!*\
  !*** ./node_modules/core-js-pure/stable/object/get-own-property-descriptors.js ***!
  \*********************************************************************************/

/*!**********************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/assertThisInitialized.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ./node_modules/core-js-pure/features/object/get-own-property-descriptor.js ***!
  \**********************************************************************************/

/*!**********************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-own-property-names-external.js ***!
  \**********************************************************************************/

/*!***********************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-get-own-property-descriptor.js ***!
  \***********************************************************************************/

/*!************************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/instance/starts-with.js ***!
  \************************************************************************************/

/*!************************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.object.get-own-property-descriptor.js ***!
  \************************************************************************************/

/*!*************************************************************************************!*\
  !*** ./node_modules/core-js-pure/modules/es.object.get-own-property-descriptors.js ***!
  \*************************************************************************************/

/*!**************************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/object/define-property.js ***!
  \**************************************************************************************/

/*!**************************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/possibleConstructorReturn.js ***!
  \**************************************************************************************/

/*!***************************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/reflect/delete-property.js ***!
  \***************************************************************************************/

/*!***************************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/helpers/esm/unsupportedIterableToArray.js ***!
  \***************************************************************************************/

/*!***************************************************************************************!*\
  !*** ./node_modules/core-js-pure/internals/object-get-own-property-names-external.js ***!
  \***************************************************************************************/

/*!****************************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/object/define-properties.js ***!
  \****************************************************************************************/

/*!****************************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/reflect/get-prototype-of.js ***!
  \****************************************************************************************/

/*!*******************************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js/object/get-own-property-descriptor.js ***!
  \*******************************************************************************************/

/*!***********************************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/object/get-own-property-symbols.js ***!
  \***********************************************************************************************/

/*!**************************************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptor.js ***!
  \**************************************************************************************************/

/*!***************************************************************************************************!*\
  !*** ./node_modules/@babel/runtime-corejs3/core-js-stable/object/get-own-property-descriptors.js ***!
  \***************************************************************************************************/

/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 */

/**
 * @license
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */

/**
 * @license
 * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * Copyright (c) 2018 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * Copyright (c) 2019 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */

/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */

/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */

/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * Unicode Bidi Engine based on the work of Alex Shensis (@asthensis)
 * MIT License
 */

/**
 * @license
 * jsPDF fileloading PlugIn
 * Copyright (c) 2018 Aras Abbasi (<EMAIL>)
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * jsPDF filters PlugIn
 * Copyright (c) 2014 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * @license
 * jsPDF virtual FileSystem functionality
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */

/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */

/** ====================================================================
 * @license
 * jsPDF XMP metadata plugin
 * Copyright (c) 2016 Jussi Utunen, <EMAIL>
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 */

/** @license
   * Copyright (c) 2017 Dominik Homberger
  Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
  The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  https://webpjs.appspot.com
  WebPRiffParser <EMAIL>
  */

/** @license
 * Copyright (c) 2012 Willow Systems Corporation, https://github.com/willowsystems
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 */
