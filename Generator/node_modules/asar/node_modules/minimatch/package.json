{"author": "<PERSON> <<EMAIL>> (http://blog.izs.me)", "name": "minimatch", "description": "a glob matcher in javascript", "version": "3.1.2", "publishConfig": {"tag": "v3-legacy"}, "repository": {"type": "git", "url": "git://github.com/isaacs/minimatch.git"}, "main": "minimatch.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --all; git push origin --tags"}, "engines": {"node": "*"}, "dependencies": {"brace-expansion": "^1.1.7"}, "devDependencies": {"tap": "^15.1.6"}, "license": "ISC", "files": ["minimatch.js"]}