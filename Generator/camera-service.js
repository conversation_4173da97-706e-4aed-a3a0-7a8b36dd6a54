// Camera Service for eHroby PWA
// Handles photo capture and upload to Firebase Storage

class CameraService {
    constructor() {
        this.stream = null;
        this.video = null;
        this.canvas = null;
        this.currentTaskId = null;
        this.isCapturing = false;
    }

    // Check if camera is supported
    isSupported() {
        return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    }

    // Initialize camera
    async initCamera() {
        if (!this.isSupported()) {
            throw new Error('Kamera nie je podporovaná v tomto prehliadači');
        }

        try {
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: { 
                    facingMode: 'environment', // Use back camera on mobile
                    width: { ideal: 1920 },
                    height: { ideal: 1080 }
                },
                audio: false
            });
            
            return this.stream;
        } catch (error) {
            console.error('Error accessing camera:', error);
            throw new Error('Nepodarilo sa získať prístup ku kamere');
        }
    }

    // Show camera modal
    async showCameraModal(taskId = null) {
        this.currentTaskId = taskId;
        
        try {
            await this.initCamera();
            this.createCameraModal();
        } catch (error) {
            this.showError(error.message);
        }
    }

    // Create camera modal UI
    createCameraModal() {
        // Remove existing modal if any
        const existingModal = document.getElementById('camera-modal');
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.id = 'camera-modal';
        modal.className = 'camera-modal';
        modal.innerHTML = `
            <div class="camera-modal-content">
                <div class="camera-header">
                    <h3><i class="fas fa-camera"></i> Odfotiť hrob</h3>
                    <button class="camera-close-btn" onclick="cameraService.closeCamera()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="camera-container">
                    <video id="camera-video" autoplay playsinline></video>
                    <canvas id="camera-canvas" style="display: none;"></canvas>
                </div>
                
                <div class="camera-controls">
                    <button class="camera-btn capture-btn" onclick="cameraService.capturePhoto()">
                        <i class="fas fa-camera"></i>
                        <span>Odfotiť</span>
                    </button>
                    <button class="camera-btn gallery-btn" onclick="cameraService.selectFromGallery()">
                        <i class="fas fa-images"></i>
                        <span>Galéria</span>
                    </button>
                </div>
                
                <div class="camera-preview" id="camera-preview" style="display: none;">
                    <img id="preview-image" src="" alt="Preview">
                    <div class="preview-controls">
                        <button class="btn btn-secondary" onclick="cameraService.retakePhoto()">
                            <i class="fas fa-redo"></i> Opakovať
                        </button>
                        <button class="btn btn-primary" onclick="cameraService.savePhoto()">
                            <i class="fas fa-save"></i> Uložiť
                        </button>
                    </div>
                </div>
                
                <div class="camera-loading" id="camera-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p>Ukladá sa fotka...</p>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        this.setupCamera();
        this.addCameraStyles();
    }

    // Setup camera video stream
    setupCamera() {
        this.video = document.getElementById('camera-video');
        this.canvas = document.getElementById('camera-canvas');
        
        if (this.video && this.stream) {
            this.video.srcObject = this.stream;
        }
    }

    // Capture photo
    capturePhoto() {
        if (!this.video || !this.canvas) return;

        const context = this.canvas.getContext('2d');
        this.canvas.width = this.video.videoWidth;
        this.canvas.height = this.video.videoHeight;
        
        context.drawImage(this.video, 0, 0);
        
        // Convert to blob
        this.canvas.toBlob((blob) => {
            this.showPreview(blob);
        }, 'image/jpeg', 0.8);
    }

    // Select photo from gallery
    selectFromGallery() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.capture = 'environment'; // Prefer camera on mobile
        
        input.onchange = (event) => {
            const file = event.target.files[0];
            if (file) {
                this.showPreview(file);
            }
        };
        
        input.click();
    }

    // Show photo preview
    showPreview(blob) {
        const preview = document.getElementById('camera-preview');
        const previewImage = document.getElementById('preview-image');
        const cameraContainer = document.querySelector('.camera-container');
        const cameraControls = document.querySelector('.camera-controls');
        
        if (preview && previewImage) {
            const url = URL.createObjectURL(blob);
            previewImage.src = url;
            
            // Hide camera, show preview
            cameraContainer.style.display = 'none';
            cameraControls.style.display = 'none';
            preview.style.display = 'block';
            
            // Store blob for saving
            this.capturedBlob = blob;
        }
    }

    // Retake photo
    retakePhoto() {
        const preview = document.getElementById('camera-preview');
        const cameraContainer = document.querySelector('.camera-container');
        const cameraControls = document.querySelector('.camera-controls');
        
        // Show camera, hide preview
        preview.style.display = 'none';
        cameraContainer.style.display = 'block';
        cameraControls.style.display = 'flex';
        
        // Clean up blob URL
        const previewImage = document.getElementById('preview-image');
        if (previewImage.src) {
            URL.revokeObjectURL(previewImage.src);
        }
        
        this.capturedBlob = null;
    }

    // Save photo
    async savePhoto() {
        if (!this.capturedBlob) return;

        const loading = document.getElementById('camera-loading');
        const preview = document.getElementById('camera-preview');
        
        try {
            // Show loading
            preview.style.display = 'none';
            loading.style.display = 'block';
            
            // Create file from blob
            const file = new File([this.capturedBlob], `hrob_${Date.now()}.jpg`, {
                type: 'image/jpeg'
            });
            
            // Upload to Firebase
            let photoData = null;
            if (window.firebaseService) {
                photoData = await window.firebaseService.uploadPhoto(file, this.currentTaskId);
            } else {
                // Fallback - store locally
                photoData = this.storePhotoLocally(file);
            }
            
            // Update task with photo
            if (this.currentTaskId && photoData) {
                await this.addPhotoToTask(this.currentTaskId, photoData);
            }
            
            this.showSuccess('Fotka bola úspešne uložená');
            this.closeCamera();
            
        } catch (error) {
            console.error('Error saving photo:', error);
            this.showError('Chyba pri ukladaní fotky');
            loading.style.display = 'none';
            preview.style.display = 'block';
        }
    }

    // Store photo locally as fallback
    storePhotoLocally(file) {
        const photoData = {
            id: 'local_' + Date.now(),
            taskId: this.currentTaskId,
            filename: file.name,
            size: file.size,
            url: URL.createObjectURL(file),
            storedAt: new Date(),
            local: true
        };
        
        // Store in localStorage for later sync
        const localPhotos = JSON.parse(localStorage.getItem('local_photos') || '[]');
        localPhotos.push(photoData);
        localStorage.setItem('local_photos', JSON.stringify(localPhotos));
        
        return photoData;
    }

    // Add photo to task
    async addPhotoToTask(taskId, photoData) {
        try {
            // Get current task
            const task = cleaningData.tasks.find(t => t.id === taskId);
            if (task) {
                if (!task.photos) {
                    task.photos = [];
                }
                task.photos.push(photoData);
                
                // Update task in Firebase
                if (window.firebaseService) {
                    await window.firebaseService.updateTask(taskId, { photos: task.photos });
                } else {
                    // Update locally
                    saveCleaningData();
                }
            }
        } catch (error) {
            console.error('Error adding photo to task:', error);
        }
    }

    // Close camera
    closeCamera() {
        // Stop camera stream
        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }
        
        // Remove modal
        const modal = document.getElementById('camera-modal');
        if (modal) {
            modal.remove();
        }
        
        // Clean up
        this.video = null;
        this.canvas = null;
        this.capturedBlob = null;
        this.currentTaskId = null;
    }

    // Add camera styles
    addCameraStyles() {
        if (document.getElementById('camera-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'camera-styles';
        styles.textContent = `
            .camera-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.9);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .camera-modal-content {
                background: white;
                border-radius: 12px;
                width: 90%;
                max-width: 500px;
                max-height: 90vh;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }
            
            .camera-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 1rem;
                background: #5e2e60;
                color: white;
            }
            
            .camera-close-btn {
                background: none;
                border: none;
                color: white;
                font-size: 1.2rem;
                cursor: pointer;
                padding: 0.5rem;
                border-radius: 4px;
            }
            
            .camera-container {
                position: relative;
                background: black;
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 300px;
            }
            
            #camera-video {
                width: 100%;
                height: auto;
                max-height: 400px;
                object-fit: cover;
            }
            
            .camera-controls {
                display: flex;
                justify-content: center;
                gap: 1rem;
                padding: 1rem;
                background: #f8f9fa;
            }
            
            .camera-btn {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.5rem;
                padding: 1rem;
                background: #5e2e60;
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                transition: all 0.3s ease;
                min-width: 80px;
            }
            
            .camera-btn:hover {
                background: #4a1e4a;
                transform: translateY(-2px);
            }
            
            .camera-btn i {
                font-size: 1.5rem;
            }
            
            .camera-preview {
                padding: 1rem;
                text-align: center;
            }
            
            #preview-image {
                max-width: 100%;
                max-height: 300px;
                border-radius: 8px;
                margin-bottom: 1rem;
            }
            
            .preview-controls {
                display: flex;
                gap: 1rem;
                justify-content: center;
            }
            
            .camera-loading {
                padding: 2rem;
                text-align: center;
            }
            
            .loading-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #5e2e60;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 1rem;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            @media (max-width: 768px) {
                .camera-modal-content {
                    width: 95%;
                    height: 90vh;
                }
                
                .camera-controls {
                    flex-direction: row;
                    gap: 0.5rem;
                }
                
                .camera-btn {
                    flex: 1;
                    min-width: auto;
                }
            }
        `;
        
        document.head.appendChild(styles);
    }

    // Show success message
    showSuccess(message) {
        if (window.showNotification) {
            window.showNotification(message, 'success');
        } else {
            alert(message);
        }
    }

    // Show error message
    showError(message) {
        if (window.showNotification) {
            window.showNotification(message, 'error');
        } else {
            alert(message);
        }
    }
}

// Create global camera service instance
const cameraService = new CameraService();

// Export for global access
window.cameraService = cameraService;

// Add camera functions to cleaning system
if (window.cleaningSystem) {
    window.cleaningSystem.capturePhoto = function(taskId) {
        cameraService.showCameraModal(taskId);
    };
}

// Global functions for HTML onclick handlers
window.capturePhoto = function(taskId) {
    cameraService.showCameraModal(taskId);
};

window.showCameraForTask = function(taskId) {
    cameraService.showCameraModal(taskId);
};

export default cameraService;
