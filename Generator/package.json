{"name": "espomienka-crm-fakturacia", "version": "2.0.0", "description": "eSpomienka - Kompletný CRM a fakturačný systém pre starostlivosť o hrobové miesta", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "keywords": ["CRM", "faktur<PERSON><PERSON>", "c<PERSON><PERSON><PERSON> pon<PERSON>y", "hrobové miesta", "PDF generátor", "desktop aplikácia", "s<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "author": {"name": "Anima mundi spol. s r.o.", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "firebase-tools": "^14.9.0"}, "dependencies": {"asar": "^3.2.0", "html2pdf.js": "^0.10.3", "jspdf": "^3.0.1"}, "build": {"appId": "com.animamundi.espomienka-crm", "productName": "eSpomienka CRM", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "node_modules/html2pdf.js/**/*", "!dist", "!.git", "!README.md"], "mac": {"category": "public.app-category.business", "icon": "assets/icon.icns", "identity": null, "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"icon": "assets/icon.ico", "target": [{"target": "nsis", "arch": ["x64", "ia32"]}]}, "linux": {"icon": "assets/icon.png", "category": "Office", "target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "eSpomienka CRM"}, "dmg": {"title": "eSpomienka CRM", "backgroundColor": "#5e2e60"}}, "homepage": ".", "repository": {"type": "git", "url": "https://github.com/your-username/cenove-ponuky-generator.git"}}