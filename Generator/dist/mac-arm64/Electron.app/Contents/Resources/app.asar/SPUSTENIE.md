# 🚀 Spustenie Electron aplikácie

## ✅ Aplikácia je funkčná!

Vaša desktop aplikácia **CenovePonuky** je úspešne vytvorená a funguje.

## 📱 Spôsoby spustenia

### 1. Development režim (odporúčané pre testovanie)
```bash
cd Generator
npm start
```
- Spustí aplikáciu priamo zo zdrojového kódu
- Umožňuje live editing a debugging
- Zobrazuje developer console

### 2. Spustenie z .app súboru (macOS)
```bash
open dist/mac-arm64/CenovePonuky.app
```
- Alebo dvojklik na súbor v Finderi
- Spustí zabalenú verziu aplikácie
- Funguje ako normálna macOS aplikácia

### 3. Spustenie z terminálu
```bash
./dist/mac-arm64/CenovePonuky.app/Contents/MacOS/CenovePonuky
```
- Priame spustenie executable súboru
- Zobrazuje console output a chyby

## 🔧 Riešenie problémov

### Aplikácia sa nespustí cez ikonu
**Riešenie:** Použite `npm start` alebo `open` príkaz z terminálu.

### Chyba "No such file or directory"
**Príčina:** Problém s medzerami v názve súboru.
**Riešenie:** Aplikácia je teraz premenovaná na `CenovePonuky` (bez medzier).

### Bezpečnostné upozornenia
**Normálne:** Tieto upozornenia sa zobrazujú len v development režime.
**Riešenie:** V produkčnej verzii (po `npm run build`) sa nezobrazujú.

## 📦 Vytvorenie distribučných súborov

### Pre testovanie (rýchle)
```bash
npm run pack
```
Vytvorí: `dist/mac-arm64/CenovePonuky.app`

### Pre distribúciu (s inštalátormi)
```bash
npm run build
```
Vytvorí: `.dmg` súbor pre distribúciu

### Pre konkrétne platformy
```bash
npm run build-win    # Windows
npm run build-mac    # macOS  
npm run build-linux  # Linux
```

## ✨ Funkcie aplikácie

### Klávesové skratky
- `Cmd+N` - Nová ponuka
- `Cmd+O` - Otvoriť ponuku
- `Cmd+S` - Uložiť ponuku
- `Cmd+P` - Generovať PDF
- `Cmd+Q` - Ukončiť aplikáciu

### Natívne funkcie
- Aplikačné menu v slovenčine
- Natívne dialógy pre súbory
- Offline fungovanie
- Systémová integrácia

## 🎯 Stav aplikácie

✅ **Webová verzia** - Plne funkčná  
✅ **Electron verzia** - Plne funkčná  
✅ **macOS build** - Úspešne vytvorený  
✅ **Spustenie** - Funguje cez `npm start` aj `.app` súbor  

## 📋 Ďalšie kroky

1. **Testovanie** - Otestujte všetky funkcie v desktop verzii
2. **Ikona** - Pridajte vlastnú ikonu aplikácie
3. **Distribúcia** - Vytvorte `.dmg` súbor pre distribúciu
4. **Windows/Linux** - Vytvorte verzie pre ostatné platformy

## 🔗 Súbory

- `main.js` - Hlavný Electron proces
- `package.json` - Konfigurácia a build nastavenia
- `dist/` - Vytvorené aplikácie
- `ELECTRON_GUIDE.md` - Detailný návod

---

**Aplikácia je pripravená na použitie!** 🎉
