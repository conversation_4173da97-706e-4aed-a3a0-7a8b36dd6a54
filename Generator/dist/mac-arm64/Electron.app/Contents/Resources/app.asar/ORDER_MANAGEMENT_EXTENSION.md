# 🎯 ROZŠÍRENIE APLIKÁCIE - SPRÁVA OBJEDNÁVOK

## ✅ Nové funkcie implementované

### 🧭 Nová navigácia
Aplikácia má teraz tri hlavné sekcie:
- **[Cenov<PERSON> ponuka]** - Pôvodná funkcionalita
- **[Správa objednávok]** - Nová sekcia pre správu úloh
- **[Nastavenia]** - Konfigurácia aplikácie

### 📊 Dashboard s prehľadom
```
┌─────────────────────────────────────┐
│  📊 PREHĽAD OBJEDNÁVOK             │
├─────────────────────────────────────┤
│ Aktívne zmluvy: 25                  │
│ Čistenia tento mesiac: 12           │
│ Čakajúce čistenia: 8                │
│ Dokončené dnes: 3                   │
└─────────────────────────────────────┘
```

### 🤖 Automatické generovanie úloh
Keď pridáte objednávku, systém automaticky vytvorí úlohy:

**Bal<PERSON> Sviatočný:**
- ✓ Čistenie po 30 dňoch
- ✓ Veľkonočné čistenie
- ✓ <PERSON>via<PERSON> všetkých svätých (1.11)
- ✓ Vianočné č<PERSON> (24.12)

**Balík Celoročný Premium:**
- ✓ Mesačné čistenia po celý rok (12 úloh)

### 📅 Kalendárny pohľad
```
MAREC 2025
│ Po │ Ut │ St │ Št │ Pi │ So │ Ne │
├────┼────┼────┼────┼────┼────┼────┤
│    │    │    │    │    │ 1  │ 2  │
│ 3  │ 4  │ 5  │ 6  │ 7  │ 8  │ 9  │
│10  │11  │12  │13  │14  │15🔴│16  │  
│17  │18  │19  │20  │21  │22  │23  │
│24  │25  │26  │27  │28  │29  │30  │

🔴 = 3 čistenia naplánované
```

### ✅ Zoznam úloh s checkboxmi
```
┌─ DNES (15.3.2025) ─────────────────┐
│ ☐ Ján Novák - Jednohrob            │
│   📍 Cintorín Bratislava           │
│   📝 Základná údržba               │
│   ⏰ 9:00                          │
│                                    │
│ ☐ Mária Svoboda - Veľké urnové     │
│   📍 Cintorín Petržalka           │
│   📝 Hĺbkové čistenie              │
│   ⏰ 14:00                         │
└────────────────────────────────────┘
```

## 🔧 Funkcionalita

### Pridanie objednávky:
1. **Import z cenovej ponuky** ✓ - Tlačidlo "Vytvoriť objednávku"
2. **Manuálne pridanie** ✓ - Formulár s výberom balíka
3. **Automatické plánovanie** ✓ - Generovanie úloh podľa balíka
4. **Uloženie do localStorage** ✓ - Trvalé uloženie dát

### Označovanie dokončených úloh:
- ✅ **Checkbox systém** - Jednoduché označenie dokončenia
- 📝 **Detail úlohy** - Zobrazenie všetkých informácií
- 📊 **Aktualizácia dashboardu** - Automatické prepočítanie štatistík

### Filtrovanie a vyhľadávanie:
- 🔍 **Hľadať:** Meno zákazníka
- 📅 **Dátum:** Dnes/Tento týždeň/Tento mesiac/Všetky
- 📍 **Lokalita:** Všetky/Bratislava/Petržalka
- 🏷️ **Typ:** Všetky/Základná/Hĺbková

## 💾 Technické riešenie

### Dátová štruktúra:
```javascript
// Objednávka
{
  id: "ORD-001",
  customer: {
    name: "Ján Novák",
    phone: "+421 901 234 567",
    email: "<EMAIL>"
  },
  location: "bratislava",
  package: "sviatocny_jednohrob",
  startDate: "2025-03-15",
  endDate: "2026-03-15",
  status: "active",
  createdAt: "2025-03-15T10:00:00Z"
}

// Úloha
{
  id: "TASK-001", 
  orderId: "ORD-001",
  date: "2025-04-15",
  type: "základná",
  description: "Základná údržba",
  status: "pending", // pending/completed/cancelled
  notes: "",
  photos: [],
  completedAt: "2025-04-15T14:30:00Z"
}
```

### Automatické plánovanie:
```javascript
function generateTasks(package, startDate) {
  switch(package) {
    case 'sviatocny':
      return [
        { offset: 30, type: 'základná', description: 'Základná údržba' },
        { date: 'easter', type: 'základná', description: 'Veľkonočné čistenie' },
        { date: '11-01', type: 'základná', description: 'Sviatok všetkých svätých' },
        { date: '12-24', type: 'základná', description: 'Vianočné čistenie' }
      ];
    case 'celorocny':
      return generateMonthlyTasks(startDate, 12);
  }
}
```

### Výpočet Veľkej noci:
```javascript
function calculateEasterDate(year) {
  // Implementovaný algoritmus pre výpočet dátumu Veľkej noci
  // Automaticky vypočíta správny dátum pre každý rok
}
```

## 📁 Nové súbory

### JavaScript:
- `orders.js` - Kompletná logika pre správu objednávok
- Rozšírený `script.js` - Podpora tab systému

### CSS:
- Nové štýly pre dashboard, úlohy, kalendár, modály
- Responzívny dizajn pre všetky nové komponenty

### HTML:
- Nová navigácia s tabmi
- Modálne okná pre pridanie objednávky a kalendár
- Dashboard s kartami štatistík

## 🚀 Ako používať

### 1. Vytvorenie objednávky z cenovej ponuky:
1. Vyplňte údaje zákazníka
2. Vyberte služby/balík
3. Kliknite "Vytvoriť objednávku"
4. Systém automaticky vytvorí úlohy

### 2. Manuálne pridanie objednávky:
1. Prejdite na "Správa objednávok"
2. Kliknite "Pridať objednávku"
3. Vyplňte formulár
4. Systém vygeneruje úlohy automaticky

### 3. Správa úloh:
1. Označte dokončené úlohy checkboxom
2. Použite filtre na vyhľadávanie
3. Kliknite na úlohu pre detail
4. Sledujte štatistiky na dashboarde

### 4. Kalendárny pohľad:
1. Kliknite "Kalendár"
2. Prechádzajte medzi mesiacmi
3. Vidíte všetky naplánované úlohy
4. Farebné označenie dní s úlohami

## 🎯 Výhody nového systému

- ✅ **Automatizácia** - Žiadne manuálne plánovanie úloh
- ✅ **Prehľadnosť** - Dashboard s kľúčovými štatistikami
- ✅ **Efektivita** - Rýchle filtrovanie a vyhľadávanie
- ✅ **Kalendár** - Vizuálny prehľad všetkých úloh
- ✅ **Integrácia** - Plynulý prechod z cenovej ponuky
- ✅ **Uloženie** - Všetky dáta sa ukladajú lokálne

## 📍 Umiestnenie finálnej aplikácie
`Generator/dist/mac-arm64/CenovePonuky.app`

Aplikácia je teraz kompletný systém pre správu cenových ponúk a objednávok! 🎉
