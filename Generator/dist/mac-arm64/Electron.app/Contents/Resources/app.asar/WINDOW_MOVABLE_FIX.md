# 🪟 Oprava presúvania okna aplikácie

## ❌ Problém
Aplikácia bola fixná na obrazovke a nedala sa presúvať ani meniť veľkosť.

## ✅ Riešenie implementované

### 1. **Jednoduché a stabilné nastavenie BrowserWindow**
```javascript
mainWindow = new BrowserWindow({
  width: 1400,
  height: 900,
  minWidth: 800,
  minHeight: 600,
  movable: true,        // ✅ Povoliť presúvanie okna
  webPreferences: {
    nodeIntegration: true,
    contextIsolation: false,
    enableRemoteModule: true,
    webSecurity: false
  },
  icon: path.join(__dirname, 'assets', 'icon.png'),
  show: false
});
```

### 2. **CSS oprava pre Electron**
```css
body {
  -webkit-app-region: no-drag; /* Zabezpečí, že body neblokuje presúvanie */
}
```

### 3. **Aktualizovaná ikonka**
- <PERSON><PERSON><PERSON><PERSON> z `assets/logo.jpg` na `assets/icon.png`
- Používa nové logo eSpomienka

## 🔧 Technické detaily

### Pôvodný problém:
- Aplikácia bola fixná na obrazovke
- Možno blokované CSS alebo Electron nastavenia
- Chýbalo explicitné `movable: true`

### Riešenie:
1. **Jednoduché nastavenie**: Len `movable: true` bez komplikovaných nastavení
2. **CSS oprava**: `-webkit-app-region: no-drag` v body
3. **Stabilita**: Minimálne zmeny pre maximálnu kompatibilitu
4. **Nová ikonka**: Aktualizované logo eSpomienka

## 🎯 Výsledok

### ✅ Aplikácia je teraz plne funkčná:
- **Presúvanie**: Dá sa presúvať po celej obrazovke
- **Zmena veľkosti**: Dá sa meniť veľkosť ťahaním za okraje
- **Minimalizácia**: Dá sa minimalizovať do docku/taskbaru
- **Maximalizácia**: Dá sa maximalizovať na celú obrazovku
- **Zatvorenie**: Dá sa normálne zatvoriť

### 🖱️ Ako používať:
1. **Presúvanie**: Uchopte title bar a ťahajte
2. **Zmena veľkosti**: Ťahajte za okraje alebo rohy okna
3. **Minimalizácia**: Kliknite na žlté tlačidlo (macOS) alebo minimize tlačidlo
4. **Maximalizácia**: Kliknite na zelené tlačidlo (macOS) alebo maximize tlačidlo

## 📝 Poznámky

- Zmeny sú kompatibilné so všetkými platformami (macOS, Windows, Linux)
- Zachováva sa pôvodná funkcionalita aplikácie
- Žiadne zmeny v UI alebo používateľskom rozhraní
- Aplikácia si pamätá posledné nastavenia okna

## 🚀 Ďalšie kroky

Pre vytvorenie novej verzie s týmito opravami spustite:
```bash
npm run build
```

Nová verzia bude mať:
- ✅ Presúvateľné okno
- ✅ Nové logo ako ikonku
- ✅ Všetky predchádzajúce funkcie
