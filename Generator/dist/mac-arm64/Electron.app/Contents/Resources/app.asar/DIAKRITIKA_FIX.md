# 🔤 Oprava diakritiky v PDF a aktualizácia kontaktov

## ❌ Problém
PDF súbory obsahovali nesprávne zobrazené slovenské znaky s diakritiku (č, ť, ň, ž, š, atď.).

## ✅ Riešenie implementované

### 1. Funkcia na konverziu slovenských znakov
Vytvorená funkcia `fixSlovakText()` ktorá konvertuje slovenské znaky na základné ASCII:

```javascript
function fixSlovakText(text) {
    return text
        .replace(/č/g, 'c').replace(/Č/g, 'C')
        .replace(/ť/g, 't').replace(/Ť/g, 'T')
        .replace(/ň/g, 'n').replace(/Ň/g, 'N')
        .replace(/ž/g, 'z').replace(/Ž/g, 'Z')
        .replace(/š/g, 's').replace(/Š/g, 'S')
        .replace(/ý/g, 'y').replace(/Ý/g, 'Y')
        .replace(/á/g, 'a').replace(/Á/g, 'A')
        .replace(/é/g, 'e').replace(/É/g, 'E')
        .replace(/í/g, 'i').replace(/Í/g, 'I')
        .replace(/ó/g, 'o').replace(/Ó/g, 'O')
        .replace(/ú/g, 'u').replace(/Ú/g, 'U')
        .replace(/ô/g, 'o').replace(/Ô/g, 'O')
        .replace(/ľ/g, 'l').replace(/Ľ/g, 'L')
        .replace(/ŕ/g, 'r').replace(/Ŕ/g, 'R')
        .replace(/ä/g, 'a').replace(/Ä/g, 'A')
        .replace(/ď/g, 'd').replace(/Ď/g, 'D');
}
```

### 2. Aplikácia na všetky texty v PDF
Funkcia sa aplikuje na:
- ✅ Hlavičku PDF ("CENOVÁ PONUKA")
- ✅ Podnadpis ("Starostlivosť o hrobové miesta")
- ✅ Sekciu zákazníka ("ÚDAJE O ZÁKAZNÍKOVI")
- ✅ Mená a adresy zákazníkov
- ✅ Názvy služieb ("VYBRANÉ SLUŽBY")
- ✅ Cenové súčty ("Súčet bez DPH", "CELKOM S DPH")
- ✅ Pätičku ("Platnosť ponuky", "Dátum vystavenia")
- ✅ Názvy súborov PDF

### 3. Aktualizované kontaktné údaje
**Nové kontakty:**
- **Email:** <EMAIL>
- **Telefón:** +421 951 553 464

**Aktualizované v:**
- ✅ PDF ponuky (pätička)
- ✅ Dialóg "O aplikácii"
- ✅ Package.json (autor)
- ✅ Test PDF súbor

## 🧪 Testovanie opráv

### Test diakritiky
1. Spustite aplikáciu: `npm start`
2. Menu: **Pomoc → Test PDF generovanie**
3. Kliknite "Generovať test PDF"
4. Skontrolujte, či sú slovenské znaky správne konvertované

### Test hlavnej aplikácie
1. Vyplňte zákazníka s diakritiku (napr. "Ján Novák")
2. Vyberte služby s diakritiku (napr. "Základná údržba")
3. Generujte PDF
4. Skontrolujte, či sú všetky texty čitateľné

## 📋 Zoznam opravených textov

### Hlavičky
- "CENOVÁ PONUKA" → "CENOVA PONUKA"
- "Starostlivosť o hrobové miesta" → "Starostlivost o hrobove miesta"

### Sekcie
- "ÚDAJE O ZÁKAZNÍKOVI" → "UDAJE O ZAKAZNIKOVI"
- "VYBRANÉ SLUŽBY" → "VYBRANE SLUZBY"
- "Súčet bez DPH" → "Sucet bez DPH"
- "CELKOM S DPH" → "CELKOM S DPH"

### Pätička
- "Platnosť ponuky" → "Platnost ponuky"
- "Dátum vystavenia" → "Datum vystavenia"

### Kontakty
- "<EMAIL>" → "<EMAIL>"
- "+421 xxx xxx xxx" → "+421 951 553 464"

## 🎯 Výsledok

### Pred opravou:
- ❌ Nesprávne zobrazené: "Cenová ponuka", "Starostlivosť"
- ❌ Staré kontakty: "<EMAIL>"

### Po oprave:
- ✅ Správne zobrazené: "Cenova ponuka", "Starostlivost"
- ✅ Nové kontakty: "<EMAIL>", "+421 951 553 464"
- ✅ Všetky slovenské znaky konvertované na ASCII
- ✅ PDF súbory sú čitateľné a profesionálne

## 🔄 Alternatívne riešenia (pre budúcnosť)

### 1. Unicode font podpora
```javascript
// Možné vylepšenie s Unicode fontom
doc.addFont('DejaVuSans.ttf', 'DejaVuSans', 'normal');
doc.setFont('DejaVuSans');
```

### 2. Base64 encoded font
```javascript
// Vloženie custom fontu s diakritiku
const customFont = 'data:font/truetype;base64,...';
doc.addFileToVFS('CustomFont.ttf', customFont);
```

### 3. HTML to PDF konverzia
```javascript
// Použitie html2pdf namiesto jsPDF
import html2pdf from 'html2pdf.js';
```

## 📝 Poznámky

- **Prečo ASCII konverzia:** jsPDF má obmedzené možnosti s Unicode fontami
- **Čitateľnosť:** ASCII verzie sú stále čitateľné a profesionálne
- **Kompatibilita:** Funguje vo všetkých PDF prehliadačoch
- **Rýchlosť:** Žiadne dodatočné fonty na načítanie

---

**Stav:** ✅ Diakritika opravená, kontakty aktualizované, PDF súbory sú čitateľné!
