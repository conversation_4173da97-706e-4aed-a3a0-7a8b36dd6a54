# Electron Desktop Aplikácia - Návod

## 🎉 Úspešná konverzia na Electron!

Va<PERSON> webová aplikácia bola úspešne konvertovaná na natívnu desktop aplikáciu pomocou Electron frameworku.

## 📁 Štruktúra Electron projektu

```
Generator/
├── main.js                 # Hlavný Electron proces
├── package.json            # NPM konfigurácia a build nastavenia
├── index.html              # Renderer proces (va<PERSON> webová aplikácia)
├── styles.css              # CSS štýly
├── script.js               # JavaScript (rozšírený o Electron API)
├── assets/                 # Ikony a zdroje
├── dist/                   # Vytvorené distribučné súbory
└── node_modules/           # NPM závislosti
```

## 🚀 Spustenie aplikácie

### Development režim
```bash
npm start
```

### Vytvorenie distribučných súborov
```bash
# Iba zabalenie (r<PERSON><PERSON><PERSON>j<PERSON><PERSON>, pre testovanie)
npm run pack

# Kompletná distribúcia s inštalátormi
npm run build

# Pre konkrétne platformy
npm run build-win    # Windows (.exe, .msi)
npm run build-mac    # macOS (.dmg)
npm run build-linux  # Linux (.AppImage, .deb)
```

## ✨ Nové funkcie v Electron verzii

### 🖥️ Natívne desktop funkcie
- **Aplikačné menu** s kompletnou navigáciou
- **Klávesové skratky** pre rýchly prístup
- **Natívne dialógy** pre ukladanie/otváranie súborov
- **Offline fungovanie** - nepotrebuje internet
- **Systémová integrácia** - ikona v dock/taskbar

### ⌨️ Klávesové skratky
- `Ctrl/Cmd + N` - Nová ponuka
- `Ctrl/Cmd + O` - Otvoriť ponuku
- `Ctrl/Cmd + S` - Uložiť ponuku
- `Ctrl/Cmd + P` - Generovať PDF
- `F11` - Celá obrazovka
- `F12` - Developer Tools

### 💾 Vylepšené ukladanie/načítavanie
- Natívne systémové dialógy
- Lepšia integrácia so súborovým systémom
- Automatické nastavenie správnych prípon súborov

## 🔧 Konfigurácia

### Zmena názvu aplikácie
V `package.json`:
```json
{
  "name": "vas-nazov-aplikacie",
  "productName": "Váš názov aplikácie",
  "build": {
    "productName": "Váš názov aplikácie"
  }
}
```

### Pridanie vlastnej ikony
1. Vytvorte ikony v požadovaných formátoch:
   - `assets/icon.png` (512x512px) - Linux
   - `assets/icon.ico` - Windows
   - `assets/icon.icns` - macOS

2. Ikony sa automaticky použijú pri build procese

### Zmena veľkosti okna
V `main.js`, funkcia `createWindow()`:
```javascript
mainWindow = new BrowserWindow({
  width: 1400,    // šírka
  height: 900,    // výška
  minWidth: 800,  // minimálna šírka
  minHeight: 600  // minimálna výška
});
```

## 📦 Distribúcia

### Lokálne testovanie
Po spustení `npm run pack` nájdete aplikáciu v:
- **macOS**: `dist/mac-arm64/Generátor cenových ponúk.app`
- **Windows**: `dist/win-unpacked/Generátor cenových ponúk.exe`
- **Linux**: `dist/linux-unpacked/cenove-ponuky-generator`

### Vytvorenie inštalátorov
```bash
npm run build
```

Vytvorí:
- **macOS**: `.dmg` súbor
- **Windows**: `.exe` inštalátor a `.msi` balíček
- **Linux**: `.AppImage` a `.deb` balíčky

## 🛠️ Riešenie problémov

### Aplikácia sa nespustí
1. Skontrolujte, či sú nainštalované závislosti: `npm install`
2. Skúste vymazať `node_modules` a znovu nainštalovať
3. Skontrolujte konzolu pre chybové hlásenia

### Build zlyhá
1. **macOS code signing**: Nastavené na `null` pre development
2. **Windows**: Možno potrebujete Visual Studio Build Tools
3. **Linux**: Skontrolujte, či máte nainštalované potrebné knižnice

### PDF generovanie nefunguje
- jsPDF knižnica je zahrnutá a funguje rovnako ako vo webovej verzii
- Skontrolujte konzolu pre chybové hlásenia

## 🔄 Aktualizácie

### Manuálne aktualizácie
1. Upravte kód
2. Spustite `npm run build`
3. Distribuujte nové súbory

### Automatické aktualizácie (pokročilé)
Pre implementáciu automatických aktualizácií môžete použiť:
- `electron-updater` knižnicu
- GitHub Releases
- Vlastný update server

## 📋 Checklist pre produkciu

- [ ] Otestovať všetky funkcie v Electron verzii
- [ ] Vytvoriť vlastné ikony aplikácie
- [ ] Nastaviť správne názvy a metadáta
- [ ] Otestovať build proces na cieľových platformách
- [ ] Vytvoriť distribučné balíčky
- [ ] Otestovať inštaláciu na čistých systémoch

## 🎯 Výhody Electron verzie

1. **Offline fungovanie** - nepotrebuje web server
2. **Natívny vzhľad** - integruje sa so systémom
3. **Lepšia bezpečnosť** - izolované prostredie
4. **Jednoduchá distribúcia** - jeden súbor na inštaláciu
5. **Systémová integrácia** - súborové asociácie, kontextové menu
6. **Výkon** - optimalizované pre desktop použitie

## 📞 Podpora

Pre technické otázky alebo problémy s Electron verziou kontaktujte vývojára.

---

**Electron verzia:** 28.3.3  
**Podporované platformy:** Windows 10+, macOS 10.15+, Ubuntu 18.04+  
**Node.js verzia:** 18.18.0+
