{"hosting": {"public": ".", "ignore": ["firebase.json", "**/.*", "**/node_modules/**", "**/*.md", "**/package*.json", "**/test-*.html", "**/dist/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "/manifest.json", "headers": [{"key": "Content-Type", "value": "application/manifest+json"}, {"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "/sw.js", "headers": [{"key": "Content-Type", "value": "application/javascript"}, {"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "**/*.js", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}, {"source": "**/*.css", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}, {"source": "**/*.png", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}, {"source": "**/*.jpg", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}, {"source": "**/*.ico", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}], "cleanUrls": true, "trailingSlash": false}}